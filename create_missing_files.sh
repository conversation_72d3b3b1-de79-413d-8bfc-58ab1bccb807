#!/bin/bash

# 创建库存管理模块文件
files=(
  "src/views/inventory/Inbound.vue"
  "src/views/inventory/Outbound.vue"
  "src/views/inventory/Monitor.vue"
  "src/views/procurement/Suppliers.vue"
  "src/views/procurement/Planning.vue"
  "src/views/procurement/Orders.vue"
  "src/views/procurement/Analysis.vue"
  "src/views/finance/Revenue.vue"
  "src/views/finance/Cost.vue"
  "src/views/finance/Profit.vue"
  "src/views/finance/Reports.vue"
  "src/views/nutrition/MealPlanning.vue"
  "src/views/nutrition/Health.vue"
  "src/views/nutrition/Analysis.vue"
  "src/views/nutrition/Safety.vue"
  "src/views/analytics/Sales.vue"
  "src/views/analytics/Users.vue"
  "src/views/analytics/Operations.vue"
  "src/views/analytics/Forecast.vue"
  "src/views/system/Users.vue"
  "src/views/system/Roles.vue"
  "src/views/system/Config.vue"
  "src/views/system/Logs.vue"
  "src/views/system/Data.vue"
)

for file in "${files[@]}"; do
  # 提取文件名和模块名
  filename=$(basename "$file" .vue)
  module=$(basename $(dirname "$file"))
  
  # 生成组件名
  component_name="${module^}${filename}"
  
  # 创建文件内容
  cat > "$file" << COMPONENT_EOF
<template>
  <div class="${module}-${filename,,}">
    <div class="page-title">${filename}</div>
    <placeholder-page 
      title="${filename}"
      description="${filename}功能正在开发中"
      icon="ToolOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: '${component_name}',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '功能特性1', description: '${filename}的主要功能特性描述' },
      { title: '功能特性2', description: '${filename}的辅助功能特性描述' },
      { title: '功能特性3', description: '${filename}的扩展功能特性描述' },
      { title: '功能特性4', description: '${filename}的高级功能特性描述' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.${module}-${filename,,} {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
COMPONENT_EOF

  echo "Created $file"
done

echo "All files created successfully!"
