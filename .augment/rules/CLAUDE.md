# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Rules

### 重要开发原则 ⚠️
**必须确保每个页面的所有功能都能正常使用后，才能进行下一个页面的开发工作。**

这意味着：
- 已实现的功能将详细记录在此文档中
- 每个页面的所有按钮必须有实际功能实现
- 所有表单必须有完整的验证和提交逻辑
- 所有的增删改查操作必须完整实现
- 所有的弹窗、下拉菜单等交互组件必须正常工作
- 不能只实现UI而没有功能逻辑

### 完成标准
一个页面被认为是"完成"必须满足：
1. 所有UI元素都已实现
2. 所有交互功能都能正常使用
3. 有合理的数据模拟和状态管理
4. 有适当的错误处理和用户反馈
5. 代码结构清晰，注释完整

## 项目概述

这是一个智慧食堂数字化管理平台的需求分析和系统设计仓库。该项目目前处于需求分析阶段，包含完整的系统规格说明文档和菜单设计清单，为后续的系统开发提供详细的技术和功能规范。

## 仓库结构说明

### 核心文档
- `智慧食堂数字化管理平台需求分析文档.md` - 主要需求分析文档，包含项目背景、功能架构、技术选型等
- `系统菜单清单.md` - 完整的系统菜单清单，供开发团队核对使用

### 文档维护原则
- 保持文档的层次结构和编号系统一致性
- 菜单ID命名遵循既定规范（管理端：M001001001，移动端：APP001001001）
- 同时更新主需求文档和菜单清单，确保同步性
- 保留emoji图标和格式，提高可读性

## 系统架构概览

### 四层技术架构
```
前端展示层 ← 业务服务层 ← 数据访问层 ← 基础设施层
```

1. **前端展示层**：管理端Web系统、移动端App、微信小程序、自助点餐终端
2. **业务服务层**：9大核心服务模块（用户、菜品、订单、支付、库存、采购、财务、营养、消息）
3. **数据访问层**：业务数据库、缓存系统、文件存储、日志系统
4. **基础设施层**：服务网关、负载均衡、监控系统、安全防护

### 核心业务模块（9大模块）

```
智慧食堂数字化管理平台
├── 🏠 首页仪表板 - 营业概览、实时监控、任务管理
├── 🍽️ 菜品管理 - 菜品信息、分类、营养、价格
├── 🛒 订餐管理 - 订单、排队、配送、支付
├── 📦 库存管理 - 食材、出入库、监控
├── 🛒 采购管理 - 供应商、计划、订单、分析
├── 💰 财务管理 - 收入、成本、利润、报表
├── 🥗 营养健康 - 配餐、健康管理、食品安全
├── 📊 数据分析 - 销售、用户、运营、预测
└── ⚙️ 系统管理 - 用户、权限、配置、日志
```

### 菜单系统设计
- **管理端Web系统**：142个菜单项（9个一级 + 34个二级 + 99个三级菜单）
- **移动端应用**：用户端（24个页面）+ 管理端移动应用（8个页面）
- **菜单ID系统**：层次化命名规范，便于开发追踪和维护

## 技术选型规范

### 推荐技术栈
- **前端**：Vue 3 + JavaScript + Ant Design Vue + Vite
- **后端**：Spring Boot 或 Node.js
- **数据库**：MySQL（主库）+ Redis（缓存）
- **消息队列**：RabbitMQ/Kafka
- **容器化**：Docker + Kubernetes
- **云服务**：阿里云OSS、腾讯云COS

### 系统架构模式
- 前后端分离架构
- 微服务架构设计
- RESTful API接口
- 分布式部署支持

## 用户角色权限体系

系统支持7种主要用户角色：
- **系统管理员**：整体系统配置和维护
- **食堂管理员**：日常运营管理
- **厨师长/后厨管理**：菜品制作和厨房管理
- **采购员**：食材采购管理
- **财务人员**：财务统计和报表
- **营养师**：营养搭配和健康管理
- **就餐用户**：点餐消费

## 开发指引

### 文档更新规范
- 修改主需求文档时，同步更新菜单清单
- 保持菜单ID命名的一致性和层次性
- 技术架构变更需要同时更新第7章技术建议
- 新增功能需要完善对应的操作按钮说明

### 开发阶段规划
项目分为4个开发阶段：
1. **第一阶段（3个月）**：核心功能（用户/菜品/订单管理，移动端UI）
2. **第二阶段（2个月）**：进阶功能（库存/采购/财务，数据分析）
3. **第三阶段（2个月）**：高级功能（营养/AI推荐，系统优化）
4. **第四阶段（1个月）**：测试和部署

### 菜单开发参考
- 使用`系统菜单清单.md`进行开发验证
- 遵循层次化菜单ID系统进行一致性命名
- 每个菜单项都包含页面路径和操作功能说明
- Web端和移动端菜单分别设计，区分明确

## 性能与安全要求

### 性能指标
- 页面加载时间：< 3秒
- 并发用户数：1000+
- 日订单处理能力：10万+
- 系统可用性：99.9%+

### 安全要求
- 数据传输加密
- 访问日志记录
- 安全审计功能
- 定期安全漏洞扫描

## 第三方集成点

### 支付平台
- 微信支付、支付宝、银联

### 云服务
- 短信服务：阿里云短信、腾讯云短信
- 地图服务：高德地图、百度地图
- 云存储：阿里云OSS、腾讯云COS

## 文档协作注意事项

在处理此仓库的文档时：
- 这是需求分析阶段的项目，专注于规格说明而非代码实现
- 保持文档的结构化和系统性
- 技术规格需要与业务需求保持一致
- 确保菜单设计的完整性和可追溯性

**技术栈选型：**
- 前端：Vue3 + JavaScript + Ant Design Vue + Vite
- 图标：Ant Design Vue
- 图表：ECharts
