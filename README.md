# 智慧食堂数字化管理平台

基于Vue 3 + Ant Design Vue + Vite构建的现代化食堂管理系统。

## 项目特性

- 🚀 基于Vue 3 + Vite，快速开发体验
- 🎨 使用Ant Design Vue组件库，界面美观易用
- 📊 集成ECharts图表，数据可视化展示
- 🔄 响应式设计，支持移动端访问
- 🌐 完整的路由系统，支持面包屑导航
- 💾 使用Pinia进行状态管理
- 🔒 完善的权限控制系统

## 功能模块

### 核心功能
- **首页仪表板** - 营业概览、实时监控、任务管理、快捷操作
- **菜品管理** - 菜品信息、分类管理、营养成分、价格管理
- **订餐管理** - 订单处理、排队管理、配送管理、支付管理
- **库存管理** - 食材管理、出入库、库存监控
- **采购管理** - 供应商管理、采购计划、订单管理、数据分析
- **财务管理** - 收入管理、成本控制、利润分析、财务报表
- **营养健康** - 营养配餐、健康管理、营养分析、食品安全
- **数据分析** - 销售分析、用户分析、运营分析、预测分析
- **系统管理** - 用户管理、角色权限、系统配置、日志管理

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
```

### 代码格式化
```bash
npm run format
```

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite
- **UI组件库**: Ant Design Vue
- **图标**: @ant-design/icons-vue
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **图表库**: ECharts + Vue-ECharts
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **开发工具**: ESLint + Prettier

## 项目结构

```
src/
├── components/          # 公共组件
├── layout/             # 布局组件
│   └── Layout.vue      # 主布局
├── router/             # 路由配置
│   └── index.js        # 路由定义
├── stores/             # Pinia状态管理
├── views/              # 页面组件
│   ├── dashboard/      # 仪表板页面
│   ├── dishes/         # 菜品管理页面
│   ├── orders/         # 订单管理页面
│   ├── inventory/      # 库存管理页面
│   ├── procurement/    # 采购管理页面
│   ├── finance/        # 财务管理页面
│   ├── nutrition/      # 营养健康页面
│   ├── analytics/      # 数据分析页面
│   └── system/         # 系统管理页面
├── utils/              # 工具函数
├── App.vue             # 根组件
├── main.js             # 入口文件
└── style.css           # 全局样式
```

## 开发进度

### ✅ 已完成功能

#### 首页仪表板模块
- ✅ 营业概览页面 - 完整的数据统计卡片、销售趋势图表、热门菜品排行
- ✅ 实时数据监控页面 - 订单状态监控、库存预警、设备状态监控、系统性能监控
- ✅ 待处理事项页面 - 任务统计、任务列表、批量处理、任务分配功能
- ✅ 快捷操作入口页面 - 常用操作快捷入口、快速导航、常用工具、系统状态概览

#### 菜品管理模块
- ✅ 菜品列表页面 - 完整的CRUD功能、搜索筛选、批量操作、图片上传、营养标签
- ✅ 菜品分类页面 - 树形分类结构、拖拽排序、分类统计、批量管理、显示设置
- ✅ 营养成分页面 - 详细营养录入、营养分析图表、健康评分、营养标签管理
- ✅ 价格管理页面 - 智能定价、批量调价、价格历史、利润分析、定价策略配置

#### 系统基础设施
- ✅ 项目架构搭建 - Vue 3 + Vite + Ant Design Vue
- ✅ 路由系统 - 完整的页面路由配置，支持嵌套路由
- ✅ 布局系统 - 响应式侧边栏、顶部导航、面包屑导航
- ✅ 组件库集成 - Ant Design Vue组件库完整集成
- ✅ 图表系统 - ECharts图表库集成和基础图表实现

### 🚧 待开发功能

- 订餐管理模块的所有页面  
- 库存管理模块的所有页面
- 采购管理模块的所有页面
- 财务管理模块的所有页面
- 营养健康模块的所有页面
- 数据分析模块的所有页面
- 系统管理模块的所有页面

## 功能特色

### 营业概览
- 今日营业数据实时统计（收入、订单量、客流量、平均客单价）
- 销售趋势图表（支持按小时/半小时查看）
- 热门菜品排行榜
- 本周vs上周数据对比
- 各时段客流分布饼图
- 数据刷新、报表导出、提醒设置功能

### 实时监控
- 订单状态实时监控（待处理、制作中、待取餐、异常订单）
- 库存预警信息展示
- 设备运行状态监控
- 系统性能指标监控（CPU、内存、磁盘、网络）
- 实时订单流量图表
- 预警日志管理

### 任务管理
- 待处理事项统计（待审核订单、库存预警、客户投诉、系统异常）
- 任务列表管理（支持筛选、排序、批量操作）
- 任务详情查看和处理
- 任务分配功能
- 任务处理结果跟踪

### 快捷操作
- 常用操作快捷入口（新增菜品、查看订单、库存盘点、财务统计）
- 快速导航到各个功能模块
- 常用工具集成（数据导出、导入、报表生成、标签打印等）
- 系统状态概览
- 快捷设置配置

## 开发规范

### 组件开发
- 使用Vue 3 Composition API
- 组件命名采用PascalCase
- Props定义要明确类型和默认值
- 合理使用ref、reactive、computed等响应式API

### 样式规范
- 使用scoped样式避免污染
- 采用Ant Design Vue设计规范
- 响应式设计，支持移动端
- 统一的颜色和间距规范

### 代码质量
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 添加必要的注释说明
- 保持代码结构清晰

## 部署说明

### 开发环境
```bash
npm run dev
```
访问 http://localhost:3000

### 生产环境
```bash
npm run build
npm run preview
```

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 联系方式

如有问题或建议，请联系项目维护者。