<template>
  <div class="dishes-category-settings">
    <div class="page-title">分类设置</div>
    <placeholder-page 
      title="分类设置"
      description="菜品分类设置功能正在开发中"
      icon="SettingOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesCategorySettings',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '分类规则配置', description: '分类命名规则和层级限制配置' },
      { title: '默认分类设置', description: '新菜品默认分类和推荐分类设置' },
      { title: '分类模板管理', description: '常用分类模板创建和管理' },
      { title: '分类权限设置', description: '不同角色的分类操作权限设置' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-category-settings {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
