<template>
  <div class="dishes-category-list">
    <div class="page-title">分类列表</div>
    <placeholder-page 
      title="分类列表"
      description="菜品分类列表功能正在开发中"
      icon="UnorderedListOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesCategoryList',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '分类层级管理', description: '支持多级分类层级结构管理' },
      { title: '分类信息编辑', description: '分类名称、描述、图标等信息编辑' },
      { title: '分类排序', description: '支持拖拽排序和自定义排序' },
      { title: '分类状态管理', description: '分类启用/禁用状态管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-category-list {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
