<template>
  <div class="dishes-add">
    <div class="page-title">新增菜品</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button @click="saveDraft">
        <save-outlined />
        保存草稿
      </a-button>
      <a-button @click="previewDish">
        <eye-outlined />
        预览效果
      </a-button>
      <a-button type="primary" @click="submitAudit">
        <check-outlined />
        提交审核
      </a-button>
      <a-button @click="cancelOperation">
        <close-outlined />
        取消操作
      </a-button>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="dish-form"
    >
      <a-row :gutter="24">
        <!-- 基础信息 -->
        <a-col :span="12">
          <a-card title="基础信息" class="form-card">
            <a-form-item label="菜品名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入菜品名称" />
            </a-form-item>
            
            <a-form-item label="菜品分类" name="categoryId">
              <a-select v-model:value="formData.categoryId" placeholder="请选择菜品分类">
                <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="菜品描述" name="description">
              <a-textarea 
                v-model:value="formData.description" 
                placeholder="请输入菜品描述"
                :rows="4"
              />
            </a-form-item>
            
            <a-form-item label="菜品标签" name="tags">
              <a-select
                v-model:value="formData.tags"
                mode="tags"
                placeholder="请选择或输入标签"
                :options="tagOptions"
              />
            </a-form-item>
          </a-card>
        </a-col>

        <!-- 图片管理 -->
        <a-col :span="12">
          <a-card title="图片管理" class="form-card">
            <a-form-item label="菜品图片" name="images">
              <a-upload
                v-model:file-list="fileList"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @preview="handlePreview"
                @remove="handleRemove"
              >
                <div v-if="fileList.length < 5">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传图片</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <!-- 营养成分 -->
        <a-col :span="12">
          <a-card title="营养成分" class="form-card">
            <a-form-item label="热量 (kcal)" name="calories">
              <a-input-number 
                v-model:value="formData.nutrition.calories" 
                :min="0" 
                style="width: 100%"
                placeholder="请输入热量"
              />
            </a-form-item>
            
            <a-form-item label="蛋白质 (g)" name="protein">
              <a-input-number 
                v-model:value="formData.nutrition.protein" 
                :min="0" 
                :precision="1"
                style="width: 100%"
                placeholder="请输入蛋白质含量"
              />
            </a-form-item>
            
            <a-form-item label="脂肪 (g)" name="fat">
              <a-input-number 
                v-model:value="formData.nutrition.fat" 
                :min="0" 
                :precision="1"
                style="width: 100%"
                placeholder="请输入脂肪含量"
              />
            </a-form-item>
            
            <a-form-item label="碳水化合物 (g)" name="carbohydrate">
              <a-input-number 
                v-model:value="formData.nutrition.carbohydrate" 
                :min="0" 
                :precision="1"
                style="width: 100%"
                placeholder="请输入碳水化合物含量"
              />
            </a-form-item>
          </a-card>
        </a-col>

        <!-- 价格策略 -->
        <a-col :span="12">
          <a-card title="价格策略" class="form-card">
            <a-form-item label="基础价格 (元)" name="price">
              <a-input-number 
                v-model:value="formData.price" 
                :min="0" 
                :precision="2"
                style="width: 100%"
                placeholder="请输入基础价格"
              />
            </a-form-item>
            
            <a-form-item label="会员价格 (元)" name="memberPrice">
              <a-input-number 
                v-model:value="formData.memberPrice" 
                :min="0" 
                :precision="2"
                style="width: 100%"
                placeholder="请输入会员价格"
              />
            </a-form-item>
            
            <a-form-item label="成本价格 (元)" name="costPrice">
              <a-input-number 
                v-model:value="formData.costPrice" 
                :min="0" 
                :precision="2"
                style="width: 100%"
                placeholder="请输入成本价格"
              />
            </a-form-item>
            
            <a-form-item label="菜品状态" name="status">
              <a-radio-group v-model:value="formData.status">
                <a-radio value="draft">草稿</a-radio>
                <a-radio value="pending">待审核</a-radio>
                <a-radio value="active">上架</a-radio>
                <a-radio value="inactive">下架</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-card>
        </a-col>
      </a-row>
    </a-form>

    <!-- 图片预览模态框 -->
    <a-modal v-model:open="previewVisible" :footer="null">
      <img alt="预览" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  SaveOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DishesAdd',
  components: {
    SaveOutlined,
    EyeOutlined,
    CheckOutlined,
    CloseOutlined,
    PlusOutlined
  },
  setup() {
    const router = useRouter()
    const formRef = ref()
    
    // 表单数据
    const formData = reactive({
      name: '',
      categoryId: undefined,
      description: '',
      tags: [],
      images: [],
      nutrition: {
        calories: undefined,
        protein: undefined,
        fat: undefined,
        carbohydrate: undefined
      },
      price: undefined,
      memberPrice: undefined,
      costPrice: undefined,
      status: 'draft'
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入菜品名称', trigger: 'blur' },
        { min: 2, max: 50, message: '菜品名称长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      categoryId: [
        { required: true, message: '请选择菜品分类', trigger: 'change' }
      ],
      price: [
        { required: true, message: '请输入基础价格', trigger: 'blur' }
      ]
    }

    // 模拟数据
    const categories = ref([
      { id: 1, name: '主食类' },
      { id: 2, name: '荤菜类' },
      { id: 3, name: '素菜类' },
      { id: 4, name: '汤羹类' },
      { id: 5, name: '饮品类' }
    ])

    const tagOptions = ref([
      { value: '招牌菜', label: '招牌菜' },
      { value: '新品', label: '新品' },
      { value: '热销', label: '热销' },
      { value: '健康', label: '健康' },
      { value: '素食', label: '素食' },
      { value: '辣', label: '辣' },
      { value: '甜', label: '甜' }
    ])

    // 图片上传相关
    const fileList = ref([])
    const previewVisible = ref(false)
    const previewImage = ref('')

    const beforeUpload = (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!')
        return false
      }
      return false // 阻止自动上传
    }

    const handlePreview = (file) => {
      previewImage.value = file.url || file.thumbUrl
      previewVisible.value = true
    }

    const handleRemove = (file) => {
      const index = fileList.value.indexOf(file)
      const newFileList = fileList.value.slice()
      newFileList.splice(index, 1)
      fileList.value = newFileList
    }

    // 操作方法
    const saveDraft = async () => {
      try {
        formData.status = 'draft'
        // 这里应该调用API保存草稿
        message.success('草稿保存成功!')
      } catch (error) {
        message.error('保存失败，请重试')
      }
    }

    const previewDish = () => {
      // 预览菜品效果
      message.info('预览功能开发中...')
    }

    const submitAudit = async () => {
      try {
        await formRef.value.validate()
        formData.status = 'pending'
        // 这里应该调用API提交审核
        message.success('提交审核成功!')
        router.push('/dishes/list')
      } catch (error) {
        message.error('请完善必填信息')
      }
    }

    const cancelOperation = () => {
      router.back()
    }

    return {
      formRef,
      formData,
      rules,
      categories,
      tagOptions,
      fileList,
      previewVisible,
      previewImage,
      beforeUpload,
      handlePreview,
      handleRemove,
      saveDraft,
      previewDish,
      submitAudit,
      cancelOperation
    }
  }
}
</script>

<style scoped>
.dishes-add {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

.action-buttons {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.dish-form {
  max-width: 1200px;
}

.form-card {
  margin-bottom: 24px;
}

.form-card :deep(.ant-card-head) {
  background-color: #fafafa;
}
</style>
