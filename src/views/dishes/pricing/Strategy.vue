<template>
  <div class="dishes-pricing-strategy">
    <div class="page-title">定价策略</div>
    <placeholder-page 
      title="定价策略"
      description="定价策略功能正在开发中"
      icon="CalculatorOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesPricingStrategy',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '成本加成定价', description: '基于成本的自动定价策略' },
      { title: '市场竞争定价', description: '参考市场价格的竞争定价策略' },
      { title: '动态定价', description: '基于需求和时间的动态定价' },
      { title: '批量定价', description: '批量菜品定价和价格调整' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-pricing-strategy {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
