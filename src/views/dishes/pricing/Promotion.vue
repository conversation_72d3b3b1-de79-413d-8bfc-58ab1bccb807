<template>
  <div class="dishes-pricing-promotion">
    <div class="page-title">促销活动设置</div>
    <placeholder-page 
      title="促销活动设置"
      description="促销活动设置功能正在开发中"
      icon="GiftOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesPricingPromotion',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '促销活动创建', description: '创建各种类型的促销活动' },
      { title: '折扣规则设置', description: '灵活的折扣规则和条件设置' },
      { title: '活动时间管理', description: '促销活动时间和有效期管理' },
      { title: '活动效果分析', description: '促销活动效果统计和分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-pricing-promotion {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
