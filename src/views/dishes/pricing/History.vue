<template>
  <div class="dishes-pricing-history">
    <div class="page-title">价格调整记录</div>
    <placeholder-page 
      title="价格调整记录"
      description="价格调整记录功能正在开发中"
      icon="HistoryOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesPricingHistory',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '价格变更历史', description: '完整的价格变更历史记录' },
      { title: '调价原因追踪', description: '记录每次调价的原因和依据' },
      { title: '价格趋势分析', description: '价格变化趋势图表分析' },
      { title: '调价影响评估', description: '价格调整对销量的影响评估' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-pricing-history {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
