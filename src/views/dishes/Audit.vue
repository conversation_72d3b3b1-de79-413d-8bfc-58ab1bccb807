<template>
  <div class="dishes-audit">
    <div class="page-title">菜品审核</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button type="primary" @click="batchApprove" :disabled="!hasSelected">
        <check-outlined />
        批量通过
      </a-button>
      <a-button danger @click="batchReject" :disabled="!hasSelected">
        <close-outlined />
        批量驳回
      </a-button>
      <a-button @click="refreshData">
        <reload-outlined />
        刷新数据
      </a-button>
    </div>

    <!-- 筛选区域 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="审核状态">
          <a-select v-model:value="filterForm.status" style="width: 120px" @change="handleFilter">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="pending">待审核</a-select-option>
            <a-select-option value="approved">已通过</a-select-option>
            <a-select-option value="rejected">已驳回</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="菜品分类">
          <a-select v-model:value="filterForm.categoryId" style="width: 120px" @change="handleFilter">
            <a-select-option value="">全部</a-select-option>
            <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="提交时间">
          <a-range-picker v-model:value="filterForm.dateRange" @change="handleFilter" />
        </a-form-item>
        
        <a-form-item label="菜品名称">
          <a-input 
            v-model:value="filterForm.keyword" 
            placeholder="请输入菜品名称"
            style="width: 200px"
            @pressEnter="handleFilter"
          />
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleFilter">
            <search-outlined />
            搜索
          </a-button>
          <a-button @click="resetFilter" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 审核列表 -->
    <a-table
      :columns="columns"
      :data-source="auditList"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'image'">
          <a-image
            :width="60"
            :height="60"
            :src="record.image"
            :preview="false"
            style="border-radius: 4px; object-fit: cover;"
          />
        </template>
        
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        
        <template v-if="column.key === 'tags'">
          <a-tag v-for="tag in record.tags" :key="tag" color="blue">
            {{ tag }}
          </a-tag>
        </template>
        
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="viewDetail(record)">
              <eye-outlined />
              查看详情
            </a-button>
            <a-button 
              v-if="record.status === 'pending'" 
              type="link" 
              size="small" 
              @click="approveItem(record)"
            >
              <check-outlined />
              通过
            </a-button>
            <a-button 
              v-if="record.status === 'pending'" 
              type="link" 
              size="small" 
              danger 
              @click="rejectItem(record)"
            >
              <close-outlined />
              驳回
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 审核详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="菜品审核详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentItem">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="菜品名称">{{ currentItem.name }}</a-descriptions-item>
          <a-descriptions-item label="菜品分类">{{ currentItem.categoryName }}</a-descriptions-item>
          <a-descriptions-item label="价格">¥{{ currentItem.price }}</a-descriptions-item>
          <a-descriptions-item label="会员价">¥{{ currentItem.memberPrice }}</a-descriptions-item>
          <a-descriptions-item label="提交人">{{ currentItem.submitter }}</a-descriptions-item>
          <a-descriptions-item label="提交时间">{{ currentItem.submitTime }}</a-descriptions-item>
          <a-descriptions-item label="菜品描述" :span="2">{{ currentItem.description }}</a-descriptions-item>
        </a-descriptions>
        
        <div style="margin-top: 16px;">
          <h4>菜品图片</h4>
          <a-image-preview-group>
            <a-image
              v-for="(img, index) in currentItem.images"
              :key="index"
              :width="100"
              :height="100"
              :src="img"
              style="margin-right: 8px; border-radius: 4px; object-fit: cover;"
            />
          </a-image-preview-group>
        </div>
        
        <div style="margin-top: 16px;">
          <h4>营养信息</h4>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="热量" :value="currentItem.nutrition.calories" suffix="kcal" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="蛋白质" :value="currentItem.nutrition.protein" suffix="g" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="脂肪" :value="currentItem.nutrition.fat" suffix="g" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="碳水化合物" :value="currentItem.nutrition.carbohydrate" suffix="g" />
            </a-col>
          </a-row>
        </div>
        
        <div style="margin-top: 24px; text-align: right;" v-if="currentItem.status === 'pending'">
          <a-space>
            <a-button @click="detailModalVisible = false">取消</a-button>
            <a-button danger @click="rejectWithComment(currentItem)">驳回</a-button>
            <a-button type="primary" @click="approveItem(currentItem)">通过审核</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 驳回原因模态框 -->
    <a-modal
      v-model:open="rejectModalVisible"
      title="驳回原因"
      @ok="confirmReject"
      @cancel="rejectModalVisible = false"
    >
      <a-form>
        <a-form-item label="驳回原因" required>
          <a-textarea 
            v-model:value="rejectReason" 
            placeholder="请输入驳回原因"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  SearchOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DishesAudit',
  components: {
    CheckOutlined,
    CloseOutlined,
    ReloadOutlined,
    SearchOutlined,
    EyeOutlined
  },
  setup() {
    const loading = ref(false)
    const detailModalVisible = ref(false)
    const rejectModalVisible = ref(false)
    const currentItem = ref(null)
    const rejectReason = ref('')
    const selectedRowKeys = ref([])
    
    // 筛选表单
    const filterForm = reactive({
      status: '',
      categoryId: '',
      dateRange: null,
      keyword: ''
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    })

    // 表格列配置
    const columns = [
      {
        title: '菜品图片',
        key: 'image',
        width: 80,
        align: 'center'
      },
      {
        title: '菜品名称',
        dataIndex: 'name',
        key: 'name',
        width: 150
      },
      {
        title: '分类',
        dataIndex: 'categoryName',
        key: 'categoryName',
        width: 100
      },
      {
        title: '价格',
        dataIndex: 'price',
        key: 'price',
        width: 80,
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '标签',
        key: 'tags',
        width: 150
      },
      {
        title: '提交人',
        dataIndex: 'submitter',
        key: 'submitter',
        width: 100
      },
      {
        title: '提交时间',
        dataIndex: 'submitTime',
        key: 'submitTime',
        width: 150
      },
      {
        title: '审核状态',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ]

    // 模拟数据
    const categories = ref([
      { id: 1, name: '主食类' },
      { id: 2, name: '荤菜类' },
      { id: 3, name: '素菜类' },
      { id: 4, name: '汤羹类' },
      { id: 5, name: '饮品类' }
    ])

    const auditList = ref([])

    // 计算属性
    const hasSelected = computed(() => selectedRowKeys.value.length > 0)

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      },
      getCheckboxProps: (record) => ({
        disabled: record.status !== 'pending'
      })
    }

    // 生成模拟数据
    const generateMockData = () => {
      const mockData = []
      const statuses = ['pending', 'approved', 'rejected']
      const names = ['宫保鸡丁', '麻婆豆腐', '红烧肉', '西红柿鸡蛋', '青椒土豆丝']
      const submitters = ['张三', '李四', '王五', '赵六']
      
      for (let i = 1; i <= 50; i++) {
        mockData.push({
          id: i,
          name: names[Math.floor(Math.random() * names.length)],
          categoryId: Math.floor(Math.random() * 5) + 1,
          categoryName: categories.value[Math.floor(Math.random() * 5)].name,
          price: (Math.random() * 30 + 10).toFixed(2),
          memberPrice: (Math.random() * 25 + 8).toFixed(2),
          image: `https://via.placeholder.com/60x60?text=菜品${i}`,
          images: [
            `https://via.placeholder.com/300x200?text=菜品${i}-1`,
            `https://via.placeholder.com/300x200?text=菜品${i}-2`
          ],
          tags: ['招牌菜', '热销'].slice(0, Math.floor(Math.random() * 2) + 1),
          description: `这是菜品${i}的详细描述，包含制作工艺和特色介绍。`,
          submitter: submitters[Math.floor(Math.random() * submitters.length)],
          submitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString(),
          status: statuses[Math.floor(Math.random() * statuses.length)],
          nutrition: {
            calories: Math.floor(Math.random() * 300 + 100),
            protein: (Math.random() * 20 + 5).toFixed(1),
            fat: (Math.random() * 15 + 2).toFixed(1),
            carbohydrate: (Math.random() * 25 + 5).toFixed(1)
          }
        })
      }
      return mockData
    }

    // 状态相关方法
    const getStatusColor = (status) => {
      const colors = {
        pending: 'orange',
        approved: 'green',
        rejected: 'red'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已通过',
        rejected: '已驳回'
      }
      return texts[status] || '未知'
    }

    // 数据加载
    const loadData = async () => {
      loading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const mockData = generateMockData()
        let filteredData = mockData

        // 应用筛选条件
        if (filterForm.status) {
          filteredData = filteredData.filter(item => item.status === filterForm.status)
        }
        if (filterForm.categoryId) {
          filteredData = filteredData.filter(item => item.categoryId === filterForm.categoryId)
        }
        if (filterForm.keyword) {
          filteredData = filteredData.filter(item => 
            item.name.toLowerCase().includes(filterForm.keyword.toLowerCase())
          )
        }

        pagination.total = filteredData.length
        const start = (pagination.current - 1) * pagination.pageSize
        const end = start + pagination.pageSize
        auditList.value = filteredData.slice(start, end)
      } catch (error) {
        message.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    // 操作方法
    const refreshData = () => {
      loadData()
    }

    const handleFilter = () => {
      pagination.current = 1
      loadData()
    }

    const resetFilter = () => {
      Object.assign(filterForm, {
        status: '',
        categoryId: '',
        dateRange: null,
        keyword: ''
      })
      handleFilter()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadData()
    }

    const viewDetail = (record) => {
      currentItem.value = record
      detailModalVisible.value = true
    }

    const approveItem = async (record) => {
      try {
        loading.value = true
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        record.status = 'approved'
        message.success('审核通过成功!')
        detailModalVisible.value = false
      } catch (error) {
        message.error('操作失败')
      } finally {
        loading.value = false
      }
    }

    const rejectItem = (record) => {
      currentItem.value = record
      rejectModalVisible.value = true
    }

    const rejectWithComment = (record) => {
      currentItem.value = record
      detailModalVisible.value = false
      rejectModalVisible.value = true
    }

    const confirmReject = async () => {
      if (!rejectReason.value.trim()) {
        message.warning('请输入驳回原因')
        return
      }

      try {
        loading.value = true
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        currentItem.value.status = 'rejected'
        currentItem.value.rejectReason = rejectReason.value
        message.success('驳回成功!')
        rejectModalVisible.value = false
        rejectReason.value = ''
      } catch (error) {
        message.error('操作失败')
      } finally {
        loading.value = false
      }
    }

    const batchApprove = async () => {
      try {
        loading.value = true
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        auditList.value.forEach(item => {
          if (selectedRowKeys.value.includes(item.id)) {
            item.status = 'approved'
          }
        })
        
        message.success(`批量通过 ${selectedRowKeys.value.length} 个菜品`)
        selectedRowKeys.value = []
      } catch (error) {
        message.error('批量操作失败')
      } finally {
        loading.value = false
      }
    }

    const batchReject = () => {
      rejectModalVisible.value = true
    }

    onMounted(() => {
      loadData()
    })

    return {
      loading,
      detailModalVisible,
      rejectModalVisible,
      currentItem,
      rejectReason,
      selectedRowKeys,
      filterForm,
      pagination,
      columns,
      categories,
      auditList,
      hasSelected,
      rowSelection,
      getStatusColor,
      getStatusText,
      refreshData,
      handleFilter,
      resetFilter,
      handleTableChange,
      viewDetail,
      approveItem,
      rejectItem,
      rejectWithComment,
      confirmReject,
      batchApprove,
      batchReject
    }
  }
}
</script>

<style scoped>
.dishes-audit {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

.action-buttons {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 24px;
}
</style>
