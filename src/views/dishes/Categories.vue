<template>
  <div class="dishes-categories">
    <div class="page-title">菜品分类</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button type="primary" @click="addCategory">
        <plus-outlined />
        新增分类
      </a-button>
      <a-button @click="expandAll">
        <branches-outlined />
        展开全部
      </a-button>
      <a-button @click="collapseAll">
        <shrink-outlined />
        收起全部
      </a-button>
      <a-button @click="sortCategories">
        <sort-ascending-outlined />
        排序管理
      </a-button>
    </div>

    <!-- 分类统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="全部分类"
            :value="categoryStats.total"
            suffix="个"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <appstore-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="启用分类"
            :value="categoryStats.enabled"
            suffix="个"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <check-circle-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="禁用分类"
            :value="categoryStats.disabled"
            suffix="个"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <stop-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="关联菜品"
            :value="categoryStats.totalDishes"
            suffix="个"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <shop-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区 -->
    <a-card style="margin-bottom: 24px;">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="6">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索分类名称、编码"
            allow-clear
            @change="onSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="分类状态"
            allow-clear
            @change="onSearch"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="enabled">启用</a-select-option>
            <a-select-option value="disabled">禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-select
            v-model:value="searchForm.level"
            placeholder="分类级别"
            allow-clear
            @change="onSearch"
          >
            <a-select-option value="">全部级别</a-select-option>
            <a-select-option value="1">一级分类</a-select-option>
            <a-select-option value="2">二级分类</a-select-option>
            <a-select-option value="3">三级分类</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="6">
          <a-space>
            <a-button type="primary" @click="onSearch">
              <search-outlined />
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <reload-outlined />
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 分类树形表格 -->
    <a-card title="分类管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新分类">
            <a-button @click="refreshCategories">
              <reload-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="分类设置">
            <a-button @click="showCategorySettings">
              <setting-outlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <a-table
        :columns="categoryColumns"
        :data-source="filteredCategories"
        :pagination="false"
        :loading="loading"
        :expanded-row-keys="expandedRowKeys"
        @expand="onExpand"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'icon'">
            <div class="category-icon" :style="{ backgroundColor: record.color }">
              <component :is="record.icon" />
            </div>
          </template>
          <template v-if="column.key === 'name'">
            <div class="category-info">
              <a-typography-text strong>{{ record.name }}</a-typography-text>
              <br />
              <a-typography-text type="secondary" style="font-size: 12px;">
                编码: {{ record.code }}
              </a-typography-text>
            </div>
          </template>
          <template v-if="column.key === 'status'">
            <a-switch
              :checked="record.status === 'enabled'"
              @change="toggleCategoryStatus(record)"
              :loading="record.updating"
            />
          </template>
          <template v-if="column.key === 'dishCount'">
            <a-badge :count="record.dishCount" show-zero>
              <a-button size="small" type="link" @click="viewCategoryDishes(record)">
                查看菜品
              </a-button>
            </a-badge>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button size="small" type="primary" @click="editCategory(record)">
                编辑
              </a-button>
              <a-button size="small" @click="addSubCategory(record)" v-if="record.level < 3">
                添加子分类
              </a-button>
              <a-dropdown>
                <a-button size="small">
                  更多
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleMoreAction">
                    <a-menu-item :key="`move-${record.id}`">
                      <drag-outlined />
                      移动分类
                    </a-menu-item>
                    <a-menu-item :key="`copy-${record.id}`">
                      <copy-outlined />
                      复制分类
                    </a-menu-item>
                    <a-menu-item :key="`export-${record.id}`">
                      <download-outlined />
                      导出数据
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item :key="`delete-${record.id}`" danger>
                      <delete-outlined />
                      删除分类
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑分类弹窗 -->
    <a-modal
      v-model:open="categoryModalVisible"
      :title="isEditing ? '编辑分类' : '新增分类'"
      width="600px"
      @ok="saveCategory"
      @cancel="closeCategoryModal"
    >
      <a-form :model="categoryForm" :rules="categoryFormRules" layout="vertical" ref="categoryFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="分类名称" name="name">
              <a-input v-model:value="categoryForm.name" placeholder="请输入分类名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分类编码" name="code">
              <a-input v-model:value="categoryForm.code" placeholder="请输入分类编码" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="上级分类" name="parentId">
              <a-tree-select
                v-model:value="categoryForm.parentId"
                :tree-data="parentCategoryOptions"
                placeholder="请选择上级分类"
                allow-clear
                tree-default-expand-all
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序权重" name="sort">
              <a-input-number
                v-model:value="categoryForm.sort"
                :min="0"
                :max="999"
                style="width: 100%"
                placeholder="数值越大排序越靠前"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="分类图标" name="icon">
              <a-select v-model:value="categoryForm.icon" placeholder="请选择图标">
                <a-select-option value="AppstoreOutlined">应用</a-select-option>
                <a-select-option value="FireOutlined">火焰</a-select-option>
                <a-select-option value="CoffeeOutlined">咖啡</a-select-option>
                <a-select-option value="GiftOutlined">礼品</a-select-option>
                <a-select-option value="HeartOutlined">爱心</a-select-option>
                <a-select-option value="StarOutlined">星星</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分类颜色" name="color">
              <a-input v-model:value="categoryForm.color" type="color" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="分类描述" name="description">
          <a-textarea
            v-model:value="categoryForm.description"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </a-form-item>
        <a-form-item label="分类状态" name="status">
          <a-radio-group v-model:value="categoryForm.status">
            <a-radio value="enabled">启用</a-radio>
            <a-radio value="disabled">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="显示设置">
          <a-checkbox-group v-model:value="categoryForm.displayOptions">
            <a-checkbox value="showInMenu">在菜单中显示</a-checkbox>
            <a-checkbox value="showInHome">在首页显示</a-checkbox>
            <a-checkbox value="allowOrder">允许下单</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 排序管理弹窗 -->
    <a-modal
      v-model:open="sortModalVisible"
      title="分类排序管理"
      width="800px"
      @ok="saveSortOrder"
      @cancel="sortModalVisible = false"
    >
      <div class="sort-container">
        <p>拖拽调整分类排序，数值越大排序越靠前</p>
        <a-list
          :data-source="sortableCategories"
          class="sortable-list"
        >
          <template #renderItem="{ item }">
            <a-list-item class="sortable-item">
              <a-list-item-meta>
                <template #avatar>
                  <div class="category-icon" :style="{ backgroundColor: item.color }">
                    <component :is="item.icon" />
                  </div>
                </template>
                <template #title>
                  {{ item.name }}
                </template>
                <template #description>
                  编码: {{ item.code }} | 菜品数: {{ item.dishCount }}
                </template>
              </a-list-item-meta>
              <div class="sort-actions">
                <a-input-number
                  v-model:value="item.sort"
                  :min="0"
                  :max="999"
                  size="small"
                  style="width: 80px; margin-right: 8px;"
                />
                <a-button size="small" @click="moveCategoryUp(item)">
                  <up-outlined />
                </a-button>
                <a-button size="small" @click="moveCategoryDown(item)">
                  <down-outlined />
                </a-button>
              </div>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-modal>

    <!-- 分类设置弹窗 -->
    <a-modal
      v-model:open="settingsModalVisible"
      title="分类设置"
      @ok="saveSettings"
      @cancel="settingsModalVisible = false"
    >
      <a-form :model="settingsForm" layout="vertical">
        <a-form-item label="默认分类展示">
          <a-radio-group v-model:value="settingsForm.defaultDisplay">
            <a-radio value="tree">树形结构</a-radio>
            <a-radio value="list">列表结构</a-radio>
            <a-radio value="card">卡片结构</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="分类层级限制">
          <a-select v-model:value="settingsForm.maxLevel">
            <a-select-option :value="2">最多2级</a-select-option>
            <a-select-option :value="3">最多3级</a-select-option>
            <a-select-option :value="4">最多4级</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="自动编码规则">
          <a-input v-model:value="settingsForm.codeRule" placeholder="如: CAT_{level}_{number}" />
        </a-form-item>
        <a-form-item label="批量操作权限">
          <a-checkbox-group v-model:value="settingsForm.batchPermissions">
            <a-checkbox value="batchEdit">批量编辑</a-checkbox>
            <a-checkbox value="batchDelete">批量删除</a-checkbox>
            <a-checkbox value="batchStatus">批量状态</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  BranchesOutlined,
  ShrinkOutlined,
  SortAscendingOutlined,
  AppstoreOutlined,
  CheckCircleOutlined,
  StopOutlined,
  ShopOutlined,
  SearchOutlined,
  ReloadOutlined,
  SettingOutlined,
  DownOutlined,
  DragOutlined,
  CopyOutlined,
  DownloadOutlined,
  DeleteOutlined,
  UpOutlined,
  FireOutlined,
  CoffeeOutlined,
  GiftOutlined,
  HeartOutlined,
  StarOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DishesCategories',
  components: {
    PlusOutlined,
    BranchesOutlined,
    ShrinkOutlined,
    SortAscendingOutlined,
    AppstoreOutlined,
    CheckCircleOutlined,
    StopOutlined,
    ShopOutlined,
    SearchOutlined,
    ReloadOutlined,
    SettingOutlined,
    DownOutlined,
    DragOutlined,
    CopyOutlined,
    DownloadOutlined,
    DeleteOutlined,
    UpOutlined,
    FireOutlined,
    CoffeeOutlined,
    GiftOutlined,
    HeartOutlined,
    StarOutlined
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const categoryModalVisible = ref(false)
    const sortModalVisible = ref(false)
    const settingsModalVisible = ref(false)
    const isEditing = ref(false)
    const expandedRowKeys = ref([1, 2, 3])
    const categoryFormRef = ref()

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      status: '',
      level: ''
    })

    // 分类统计
    const categoryStats = reactive({
      total: 15,
      enabled: 12,
      disabled: 3,
      totalDishes: 186
    })

    // 分类表单
    const categoryForm = reactive({
      id: null,
      name: '',
      code: '',
      parentId: null,
      sort: 0,
      icon: 'AppstoreOutlined',
      color: '#1890ff',
      description: '',
      status: 'enabled',
      displayOptions: ['showInMenu', 'allowOrder']
    })

    // 设置表单
    const settingsForm = reactive({
      defaultDisplay: 'tree',
      maxLevel: 3,
      codeRule: 'CAT_{level}_{number}',
      batchPermissions: ['batchEdit', 'batchStatus']
    })

    // 表单验证规则
    const categoryFormRules = {
      name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
      code: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
      sort: [{ required: true, message: '请输入排序权重', trigger: 'blur' }],
      status: [{ required: true, message: '请选择分类状态', trigger: 'change' }]
    }

    // 分类数据
    const categories = ref([
      {
        id: 1,
        name: '热菜',
        code: 'HOT_DISHES',
        parentId: null,
        level: 1,
        sort: 100,
        icon: 'FireOutlined',
        color: '#ff4d4f',
        description: '各类热菜，包括炒菜、煎菜等',
        status: 'enabled',
        dishCount: 85,
        displayOptions: ['showInMenu', 'showInHome', 'allowOrder'],
        children: [
          {
            id: 11,
            name: '川菜',
            code: 'SICHUAN_CUISINE',
            parentId: 1,
            level: 2,
            sort: 90,
            icon: 'FireOutlined',
            color: '#fa541c',
            description: '正宗川菜',
            status: 'enabled',
            dishCount: 35,
            displayOptions: ['showInMenu', 'allowOrder']
          },
          {
            id: 12,
            name: '粤菜',
            code: 'CANTONESE_CUISINE',
            parentId: 1,
            level: 2,
            sort: 80,
            icon: 'StarOutlined',
            color: '#faad14',
            description: '精致粤菜',
            status: 'enabled',
            dishCount: 28,
            displayOptions: ['showInMenu', 'allowOrder']
          },
          {
            id: 13,
            name: '家常菜',
            code: 'HOME_COOKING',
            parentId: 1,
            level: 2,
            sort: 70,
            icon: 'HeartOutlined',
            color: '#52c41a',
            description: '家常风味菜品',
            status: 'enabled',
            dishCount: 22,
            displayOptions: ['showInMenu', 'allowOrder']
          }
        ]
      },
      {
        id: 2,
        name: '凉菜',
        code: 'COLD_DISHES',
        parentId: null,
        level: 1,
        sort: 90,
        icon: 'AppstoreOutlined',
        color: '#1890ff',
        description: '各类凉菜和拌菜',
        status: 'enabled',
        dishCount: 32,
        displayOptions: ['showInMenu', 'allowOrder'],
        children: [
          {
            id: 21,
            name: '凉拌菜',
            code: 'COLD_SALAD',
            parentId: 2,
            level: 2,
            sort: 90,
            icon: 'GiftOutlined',
            color: '#13c2c2',
            description: '清爽凉拌菜',
            status: 'enabled',
            dishCount: 18,
            displayOptions: ['showInMenu', 'allowOrder']
          },
          {
            id: 22,
            name: '卤味',
            code: 'BRAISED_DISHES',
            parentId: 2,
            level: 2,
            sort: 80,
            icon: 'CoffeeOutlined',
            color: '#722ed1',
            description: '各类卤制品',
            status: 'enabled',
            dishCount: 14,
            displayOptions: ['showInMenu', 'allowOrder']
          }
        ]
      },
      {
        id: 3,
        name: '汤类',
        code: 'SOUPS',
        parentId: null,
        level: 1,
        sort: 80,
        icon: 'CoffeeOutlined',
        color: '#52c41a',
        description: '各类汤品',
        status: 'enabled',
        dishCount: 28,
        displayOptions: ['showInMenu', 'allowOrder'],
        children: []
      },
      {
        id: 4,
        name: '主食',
        code: 'STAPLE_FOOD',
        parentId: null,
        level: 1,
        sort: 70,
        icon: 'GiftOutlined',
        color: '#faad14',
        description: '米饭、面食等主食',
        status: 'enabled',
        dishCount: 24,
        displayOptions: ['showInMenu', 'allowOrder'],
        children: []
      },
      {
        id: 5,
        name: '饮品',
        code: 'BEVERAGES',
        parentId: null,
        level: 1,
        sort: 60,
        icon: 'HeartOutlined',
        color: '#eb2f96',
        description: '各类饮品',
        status: 'disabled',
        dishCount: 17,
        displayOptions: ['showInMenu'],
        children: []
      }
    ])

    // 表格列配置
    const categoryColumns = [
      {
        title: '图标',
        dataIndex: 'icon',
        key: 'icon',
        width: 80,
        fixed: 'left'
      },
      {
        title: '分类信息',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        fixed: 'left'
      },
      {
        title: '排序',
        dataIndex: 'sort',
        key: 'sort',
        width: 80,
        sorter: (a, b) => a.sort - b.sort
      },
      {
        title: '层级',
        dataIndex: 'level',
        key: 'level',
        width: 80,
        render: (text) => `${text}级`
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '菜品数量',
        dataIndex: 'dishCount',
        key: 'dishCount',
        width: 120,
        sorter: (a, b) => a.dishCount - b.dishCount
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 200,
        ellipsis: true
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ]

    // 过滤后的分类数据
    const filteredCategories = computed(() => {
      let filtered = categories.value

      if (searchForm.keyword) {
        filtered = filtered.filter(category => 
          category.name.includes(searchForm.keyword) || 
          category.code.includes(searchForm.keyword)
        )
      }

      if (searchForm.status) {
        filtered = filtered.filter(category => category.status === searchForm.status)
      }

      if (searchForm.level) {
        filtered = filtered.filter(category => category.level === parseInt(searchForm.level))
      }

      return filtered
    })

    // 上级分类选项
    const parentCategoryOptions = computed(() => {
      const buildTreeData = (categories, level = 1) => {
        return categories
          .filter(cat => cat.level === level && (!categoryForm.id || cat.id !== categoryForm.id))
          .map(cat => ({
            title: cat.name,
            value: cat.id,
            key: cat.id,
            children: level < 2 ? buildTreeData(categories, level + 1).filter(child => 
              categories.find(parent => parent.id === cat.id && parent.children?.some(c => c.id === child.value))
            ) : []
          }))
      }
      return buildTreeData(getAllCategories())
    })

    // 获取所有分类(扁平化)
    const getAllCategories = () => {
      const flattenCategories = (categories) => {
        let result = []
        categories.forEach(category => {
          result.push(category)
          if (category.children) {
            result = result.concat(flattenCategories(category.children))
          }
        })
        return result
      }
      return flattenCategories(categories.value)
    }

    // 可排序的分类列表
    const sortableCategories = computed(() => {
      return getAllCategories().sort((a, b) => b.sort - a.sort)
    })

    // 搜索功能
    const onSearch = () => {
      message.success('搜索完成')
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        keyword: '',
        status: '',
        level: ''
      })
      message.success('搜索条件已重置')
    }

    // 展开/收起功能
    const onExpand = (expanded, record) => {
      if (expanded) {
        expandedRowKeys.value.push(record.id)
      } else {
        const index = expandedRowKeys.value.indexOf(record.id)
        if (index > -1) {
          expandedRowKeys.value.splice(index, 1)
        }
      }
    }

    const expandAll = () => {
      expandedRowKeys.value = categories.value.map(cat => cat.id)
      message.success('已展开所有分类')
    }

    const collapseAll = () => {
      expandedRowKeys.value = []
      message.success('已收起所有分类')
    }

    // 新增分类
    const addCategory = () => {
      isEditing.value = false
      resetCategoryForm()
      categoryModalVisible.value = true
    }

    // 添加子分类
    const addSubCategory = (parent) => {
      isEditing.value = false
      resetCategoryForm()
      categoryForm.parentId = parent.id
      categoryModalVisible.value = true
    }

    // 编辑分类
    const editCategory = (record) => {
      isEditing.value = true
      Object.assign(categoryForm, record)
      categoryModalVisible.value = true
    }

    // 重置分类表单
    const resetCategoryForm = () => {
      Object.assign(categoryForm, {
        id: null,
        name: '',
        code: '',
        parentId: null,
        sort: 0,
        icon: 'AppstoreOutlined',
        color: '#1890ff',
        description: '',
        status: 'enabled',
        displayOptions: ['showInMenu', 'allowOrder']
      })
    }

    // 关闭分类弹窗
    const closeCategoryModal = () => {
      categoryModalVisible.value = false
      resetCategoryForm()
    }

    // 保存分类
    const saveCategory = async () => {
      try {
        await categoryFormRef.value.validate()
        
        if (isEditing.value) {
          // 更新分类
          const updateCategory = (categories, targetId, updateData) => {
            for (let category of categories) {
              if (category.id === targetId) {
                Object.assign(category, updateData, { updatedAt: new Date().toLocaleString() })
                return true
              }
              if (category.children && updateCategory(category.children, targetId, updateData)) {
                return true
              }
            }
            return false
          }
          updateCategory(categories.value, categoryForm.id, categoryForm)
          message.success('分类更新成功')
        } else {
          // 新增分类
          const newCategory = {
            ...categoryForm,
            id: Date.now(),
            level: categoryForm.parentId ? 2 : 1,
            dishCount: 0,
            children: [],
            createdAt: new Date().toLocaleString(),
            updatedAt: new Date().toLocaleString()
          }
          
          if (categoryForm.parentId) {
            // 添加到父分类的children中
            const addToParent = (categories, parentId, newCat) => {
              for (let category of categories) {
                if (category.id === parentId) {
                  if (!category.children) category.children = []
                  category.children.push(newCat)
                  return true
                }
                if (category.children && addToParent(category.children, parentId, newCat)) {
                  return true
                }
              }
              return false
            }
            addToParent(categories.value, categoryForm.parentId, newCategory)
          } else {
            categories.value.push(newCategory)
          }
          
          categoryStats.total++
          if (newCategory.status === 'enabled') {
            categoryStats.enabled++
          } else {
            categoryStats.disabled++
          }
          message.success('分类添加成功')
        }
        
        closeCategoryModal()
      } catch (error) {
        message.error('请检查表单信息')
      }
    }

    // 切换分类状态
    const toggleCategoryStatus = (record) => {
      record.updating = true
      setTimeout(() => {
        record.status = record.status === 'enabled' ? 'disabled' : 'enabled'
        record.updating = false
        record.updatedAt = new Date().toLocaleString()
        message.success(`分类状态修改成功: ${record.name}`)
      }, 500)
    }

    // 查看分类菜品
    const viewCategoryDishes = (record) => {
      message.info(`查看分类菜品: ${record.name} (${record.dishCount}个)`)
    }

    // 排序管理
    const sortCategories = () => {
      sortModalVisible.value = true
    }

    const moveCategoryUp = (item) => {
      item.sort += 10
      message.success(`${item.name} 排序提升`)
    }

    const moveCategoryDown = (item) => {
      if (item.sort > 0) {
        item.sort -= 10
        message.success(`${item.name} 排序降低`)
      }
    }

    const saveSortOrder = () => {
      // 保存排序
      sortableCategories.value.forEach((category, index) => {
        const updateCategory = (categories, targetId, sort) => {
          for (let cat of categories) {
            if (cat.id === targetId) {
              cat.sort = sort
              return true
            }
            if (cat.children && updateCategory(cat.children, targetId, sort)) {
              return true
            }
          }
          return false
        }
        updateCategory(categories.value, category.id, category.sort)
      })
      sortModalVisible.value = false
      message.success('排序保存成功')
    }

    // 分类设置
    const showCategorySettings = () => {
      settingsModalVisible.value = true
    }

    const saveSettings = () => {
      settingsModalVisible.value = false
      message.success('设置保存成功')
    }

    // 刷新分类
    const refreshCategories = () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        message.success('分类数据刷新成功')
      }, 1000)
    }

    // 处理更多操作
    const handleMoreAction = ({ key }) => {
      const [action, id] = key.split('-')
      const category = getAllCategories().find(c => c.id === parseInt(id))
      
      switch (action) {
        case 'move':
          message.info(`移动分类: ${category.name}`)
          break
        case 'copy':
          const copiedCategory = {
            ...category,
            id: Date.now(),
            name: `${category.name} - 副本`,
            code: `${category.code}_COPY`,
            createdAt: new Date().toLocaleString(),
            updatedAt: new Date().toLocaleString()
          }
          categories.value.push(copiedCategory)
          categoryStats.total++
          message.success(`分类复制成功: ${copiedCategory.name}`)
          break
        case 'export':
          message.success(`导出分类数据: ${category.name}`)
          break
        case 'delete':
          if (category.dishCount > 0) {
            message.error('该分类下有菜品，无法删除')
            return
          }
          // 删除逻辑
          const deleteCategory = (categories, targetId) => {
            for (let i = 0; i < categories.length; i++) {
              if (categories[i].id === targetId) {
                categories.splice(i, 1)
                return true
              }
              if (categories[i].children && deleteCategory(categories[i].children, targetId)) {
                return true
              }
            }
            return false
          }
          deleteCategory(categories.value, parseInt(id))
          categoryStats.total--
          message.success(`分类删除成功: ${category.name}`)
          break
      }
    }

    onMounted(() => {
      // 初始化数据
    })

    return {
      loading,
      categoryModalVisible,
      sortModalVisible,
      settingsModalVisible,
      isEditing,
      expandedRowKeys,
      categoryFormRef,
      searchForm,
      categoryStats,
      categoryForm,
      settingsForm,
      categoryFormRules,
      categories,
      categoryColumns,
      filteredCategories,
      parentCategoryOptions,
      sortableCategories,
      onSearch,
      resetSearch,
      onExpand,
      expandAll,
      collapseAll,
      addCategory,
      addSubCategory,
      editCategory,
      closeCategoryModal,
      saveCategory,
      toggleCategoryStatus,
      viewCategoryDishes,
      sortCategories,
      moveCategoryUp,
      moveCategoryDown,
      saveSortOrder,
      showCategorySettings,
      saveSettings,
      refreshCategories,
      handleMoreAction
    }
  }
}
</script>

<style scoped>
.stat-card {
  transition: all 0.3s;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.category-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
}

.category-info {
  min-width: 120px;
}

.sort-container {
  max-height: 400px;
  overflow-y: auto;
}

.sortable-list .sortable-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 8px 16px;
  background: #fafafa;
  transition: all 0.3s;
}

.sortable-list .sortable-item:hover {
  background: #f0f9ff;
  border-color: #1890ff;
}

.sort-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>