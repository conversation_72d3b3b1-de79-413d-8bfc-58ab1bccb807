<template>
  <div class="dishes-edit">
    <div class="page-title">菜品编辑</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button @click="saveModification">
        <save-outlined />
        保存修改
      </a-button>
      <a-button @click="restoreVersion">
        <rollback-outlined />
        恢复版本
      </a-button>
      <a-button @click="viewChangeLog">
        <history-outlined />
        查看变更日志
      </a-button>
      <a-button @click="goBack">
        <arrow-left-outlined />
        返回列表
      </a-button>
    </div>

    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        class="dish-form"
      >
        <a-row :gutter="24">
          <!-- 基础信息 -->
          <a-col :span="12">
            <a-card title="基础信息" class="form-card">
              <a-form-item label="菜品名称" name="name">
                <a-input v-model:value="formData.name" placeholder="请输入菜品名称" />
              </a-form-item>
              
              <a-form-item label="菜品分类" name="categoryId">
                <a-select v-model:value="formData.categoryId" placeholder="请选择菜品分类">
                  <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="菜品描述" name="description">
                <a-textarea 
                  v-model:value="formData.description" 
                  placeholder="请输入菜品描述"
                  :rows="4"
                />
              </a-form-item>
              
              <a-form-item label="菜品标签" name="tags">
                <a-select
                  v-model:value="formData.tags"
                  mode="tags"
                  placeholder="请选择或输入标签"
                  :options="tagOptions"
                />
              </a-form-item>
            </a-card>
          </a-col>

          <!-- 图片管理 -->
          <a-col :span="12">
            <a-card title="图片管理" class="form-card">
              <a-form-item label="菜品图片" name="images">
                <a-upload
                  v-model:file-list="fileList"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  @preview="handlePreview"
                  @remove="handleRemove"
                >
                  <div v-if="fileList.length < 5">
                    <plus-outlined />
                    <div style="margin-top: 8px">上传图片</div>
                  </div>
                </a-upload>
              </a-form-item>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <!-- 营养成分 -->
          <a-col :span="12">
            <a-card title="营养成分" class="form-card">
              <a-form-item label="热量 (kcal)" name="calories">
                <a-input-number 
                  v-model:value="formData.nutrition.calories" 
                  :min="0" 
                  style="width: 100%"
                  placeholder="请输入热量"
                />
              </a-form-item>
              
              <a-form-item label="蛋白质 (g)" name="protein">
                <a-input-number 
                  v-model:value="formData.nutrition.protein" 
                  :min="0" 
                  :precision="1"
                  style="width: 100%"
                  placeholder="请输入蛋白质含量"
                />
              </a-form-item>
              
              <a-form-item label="脂肪 (g)" name="fat">
                <a-input-number 
                  v-model:value="formData.nutrition.fat" 
                  :min="0" 
                  :precision="1"
                  style="width: 100%"
                  placeholder="请输入脂肪含量"
                />
              </a-form-item>
              
              <a-form-item label="碳水化合物 (g)" name="carbohydrate">
                <a-input-number 
                  v-model:value="formData.nutrition.carbohydrate" 
                  :min="0" 
                  :precision="1"
                  style="width: 100%"
                  placeholder="请输入碳水化合物含量"
                />
              </a-form-item>
            </a-card>
          </a-col>

          <!-- 价格策略 -->
          <a-col :span="12">
            <a-card title="价格策略" class="form-card">
              <a-form-item label="基础价格 (元)" name="price">
                <a-input-number 
                  v-model:value="formData.price" 
                  :min="0" 
                  :precision="2"
                  style="width: 100%"
                  placeholder="请输入基础价格"
                />
              </a-form-item>
              
              <a-form-item label="会员价格 (元)" name="memberPrice">
                <a-input-number 
                  v-model:value="formData.memberPrice" 
                  :min="0" 
                  :precision="2"
                  style="width: 100%"
                  placeholder="请输入会员价格"
                />
              </a-form-item>
              
              <a-form-item label="成本价格 (元)" name="costPrice">
                <a-input-number 
                  v-model:value="formData.costPrice" 
                  :min="0" 
                  :precision="2"
                  style="width: 100%"
                  placeholder="请输入成本价格"
                />
              </a-form-item>
              
              <a-form-item label="菜品状态" name="status">
                <a-radio-group v-model:value="formData.status">
                  <a-radio value="draft">草稿</a-radio>
                  <a-radio value="pending">待审核</a-radio>
                  <a-radio value="active">上架</a-radio>
                  <a-radio value="inactive">下架</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-card>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>

    <!-- 图片预览模态框 -->
    <a-modal v-model:open="previewVisible" :footer="null">
      <img alt="预览" style="width: 100%" :src="previewImage" />
    </a-modal>

    <!-- 版本恢复模态框 -->
    <a-modal
      v-model:open="versionModalVisible"
      title="版本恢复"
      @ok="confirmRestore"
      @cancel="versionModalVisible = false"
    >
      <a-list :data-source="versionHistory" item-layout="horizontal">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <a-radio v-model:checked="selectedVersion" :value="item.id">
                  版本 {{ item.version }}
                </a-radio>
              </template>
              <template #description>
                {{ item.updateTime }} - {{ item.updateBy }} - {{ item.description }}
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  RollbackOutlined,
  HistoryOutlined,
  ArrowLeftOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DishesEdit',
  components: {
    SaveOutlined,
    RollbackOutlined,
    HistoryOutlined,
    ArrowLeftOutlined,
    PlusOutlined
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref()
    const loading = ref(false)
    const versionModalVisible = ref(false)
    const selectedVersion = ref(null)
    
    // 表单数据
    const formData = reactive({
      id: null,
      name: '',
      categoryId: undefined,
      description: '',
      tags: [],
      images: [],
      nutrition: {
        calories: undefined,
        protein: undefined,
        fat: undefined,
        carbohydrate: undefined
      },
      price: undefined,
      memberPrice: undefined,
      costPrice: undefined,
      status: 'active'
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入菜品名称', trigger: 'blur' },
        { min: 2, max: 50, message: '菜品名称长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      categoryId: [
        { required: true, message: '请选择菜品分类', trigger: 'change' }
      ],
      price: [
        { required: true, message: '请输入基础价格', trigger: 'blur' }
      ]
    }

    // 模拟数据
    const categories = ref([
      { id: 1, name: '主食类' },
      { id: 2, name: '荤菜类' },
      { id: 3, name: '素菜类' },
      { id: 4, name: '汤羹类' },
      { id: 5, name: '饮品类' }
    ])

    const tagOptions = ref([
      { value: '招牌菜', label: '招牌菜' },
      { value: '新品', label: '新品' },
      { value: '热销', label: '热销' },
      { value: '健康', label: '健康' },
      { value: '素食', label: '素食' },
      { value: '辣', label: '辣' },
      { value: '甜', label: '甜' }
    ])

    const versionHistory = ref([
      {
        id: 1,
        version: '1.0',
        updateTime: '2024-01-15 10:30:00',
        updateBy: '张三',
        description: '初始版本创建'
      },
      {
        id: 2,
        version: '1.1',
        updateTime: '2024-01-16 14:20:00',
        updateBy: '李四',
        description: '更新价格和营养信息'
      }
    ])

    // 图片上传相关
    const fileList = ref([])
    const previewVisible = ref(false)
    const previewImage = ref('')

    // 加载菜品数据
    const loadDishData = async () => {
      loading.value = true
      try {
        const dishId = route.params.id
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟数据
        Object.assign(formData, {
          id: dishId,
          name: '宫保鸡丁',
          categoryId: 2,
          description: '经典川菜，鸡肉嫩滑，花生香脆，口感丰富',
          tags: ['招牌菜', '热销', '辣'],
          nutrition: {
            calories: 280,
            protein: 25.5,
            fat: 15.2,
            carbohydrate: 12.8
          },
          price: 18.00,
          memberPrice: 16.20,
          costPrice: 12.00,
          status: 'active'
        })
        
        fileList.value = [
          {
            uid: '1',
            name: 'dish1.jpg',
            status: 'done',
            url: 'https://via.placeholder.com/300x200'
          }
        ]
      } catch (error) {
        message.error('加载菜品数据失败')
      } finally {
        loading.value = false
      }
    }

    const beforeUpload = (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!')
        return false
      }
      return false
    }

    const handlePreview = (file) => {
      previewImage.value = file.url || file.thumbUrl
      previewVisible.value = true
    }

    const handleRemove = (file) => {
      const index = fileList.value.indexOf(file)
      const newFileList = fileList.value.slice()
      newFileList.splice(index, 1)
      fileList.value = newFileList
    }

    // 操作方法
    const saveModification = async () => {
      try {
        await formRef.value.validate()
        loading.value = true
        // 这里应该调用API保存修改
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success('保存成功!')
      } catch (error) {
        message.error('保存失败，请重试')
      } finally {
        loading.value = false
      }
    }

    const restoreVersion = () => {
      versionModalVisible.value = true
    }

    const confirmRestore = async () => {
      if (!selectedVersion.value) {
        message.warning('请选择要恢复的版本')
        return
      }
      
      try {
        loading.value = true
        // 这里应该调用API恢复版本
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success('版本恢复成功!')
        versionModalVisible.value = false
        await loadDishData()
      } catch (error) {
        message.error('版本恢复失败')
      } finally {
        loading.value = false
      }
    }

    const viewChangeLog = () => {
      message.info('变更日志功能开发中...')
    }

    const goBack = () => {
      router.push('/dishes/list')
    }

    onMounted(() => {
      loadDishData()
    })

    return {
      formRef,
      loading,
      formData,
      rules,
      categories,
      tagOptions,
      versionHistory,
      versionModalVisible,
      selectedVersion,
      fileList,
      previewVisible,
      previewImage,
      beforeUpload,
      handlePreview,
      handleRemove,
      saveModification,
      restoreVersion,
      confirmRestore,
      viewChangeLog,
      goBack
    }
  }
}
</script>

<style scoped>
.dishes-edit {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

.action-buttons {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.dish-form {
  max-width: 1200px;
}

.form-card {
  margin-bottom: 24px;
}

.form-card :deep(.ant-card-head) {
  background-color: #fafafa;
}
</style>
