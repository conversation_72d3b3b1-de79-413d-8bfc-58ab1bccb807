<template>
  <div class="dishes-nutrition">
    <div class="page-title">营养成分</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button type="primary" @click="addNutrition">
        <plus-outlined />
        添加营养信息
      </a-button>
      <a-button @click="batchAnalysis">
        <experiment-outlined />
        批量分析
      </a-button>
      <a-button @click="nutritionReport">
        <file-text-outlined />
        营养报告
      </a-button>
      <a-button @click="importNutrition">
        <upload-outlined />
        导入数据
      </a-button>
    </div>

    <!-- 营养统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="已录入菜品"
            :value="nutritionStats.recorded"
            suffix="个"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <check-circle-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="待录入菜品"
            :value="nutritionStats.pending"
            suffix="个"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <clock-circle-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="高营养菜品"
            :value="nutritionStats.highNutrition"
            suffix="个"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <heart-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="平均热量"
            :value="nutritionStats.avgCalories"
            suffix="kcal"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <fire-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区 -->
    <a-card style="margin-bottom: 24px;">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="6">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索菜品名称"
            allow-clear
            @change="onSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-select
            v-model:value="searchForm.category"
            placeholder="菜品分类"
            allow-clear
            @change="onSearch"
          >
            <a-select-option value="">全部分类</a-select-option>
            <a-select-option value="hot">热菜</a-select-option>
            <a-select-option value="cold">凉菜</a-select-option>
            <a-select-option value="soup">汤类</a-select-option>
            <a-select-option value="staple">主食</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-select
            v-model:value="searchForm.nutritionLevel"
            placeholder="营养等级"
            allow-clear
            @change="onSearch"
          >
            <a-select-option value="">全部等级</a-select-option>
            <a-select-option value="high">高营养</a-select-option>
            <a-select-option value="medium">中营养</a-select-option>
            <a-select-option value="low">低营养</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="6">
          <a-range-picker
            v-model:value="searchForm.caloriesRange"
            placeholder="['最低热量', '最高热量']"
            @change="onSearch"
          />
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-space>
            <a-button type="primary" @click="onSearch">
              <search-outlined />
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <reload-outlined />
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 营养成分列表 -->
    <a-card title="营养成分管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新数据">
            <a-button @click="refreshData">
              <reload-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="营养分析">
            <a-button @click="showNutritionAnalysis">
              <bar-chart-outlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <a-table
        :columns="nutritionColumns"
        :data-source="filteredNutrition"
        :pagination="pagination"
        :loading="loading"
        :scroll="{ x: 1800 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'dishInfo'">
            <div class="dish-info">
              <a-avatar :src="record.image" size="small" style="margin-right: 8px;" />
              <div>
                <a-typography-text strong>{{ record.dishName }}</a-typography-text>
                <br />
                <a-tag :color="getCategoryColor(record.category)" size="small">
                  {{ getCategoryText(record.category) }}
                </a-tag>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'calories'">
            <div class="calories-info">
              <div class="calories-value">{{ record.calories }}</div>
              <div class="calories-unit">kcal/100g</div>
            </div>
          </template>
          <template v-if="column.key === 'macronutrients'">
            <div class="macro-nutrients">
              <div class="nutrient-item">
                <span class="nutrient-label">蛋白质</span>
                <span class="nutrient-value">{{ record.protein }}g</span>
              </div>
              <div class="nutrient-item">
                <span class="nutrient-label">脂肪</span>
                <span class="nutrient-value">{{ record.fat }}g</span>
              </div>
              <div class="nutrient-item">
                <span class="nutrient-label">碳水</span>
                <span class="nutrient-value">{{ record.carbohydrate }}g</span>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'vitamins'">
            <a-space direction="vertical" size="small">
              <a-tag v-for="vitamin in record.vitamins" :key="vitamin.name" size="small">
                {{ vitamin.name }}: {{ vitamin.value }}{{ vitamin.unit }}
              </a-tag>
            </a-space>
          </template>
          <template v-if="column.key === 'minerals'">
            <a-space direction="vertical" size="small">
              <a-tag v-for="mineral in record.minerals" :key="mineral.name" size="small" color="green">
                {{ mineral.name }}: {{ mineral.value }}{{ mineral.unit }}
              </a-tag>
            </a-space>
          </template>
          <template v-if="column.key === 'nutritionLevel'">
            <a-tag :color="getNutritionLevelColor(record.nutritionLevel)">
              {{ getNutritionLevelText(record.nutritionLevel) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'healthScore'">
            <div class="health-score">
              <a-progress
                type="circle"
                :percent="record.healthScore"
                :width="50"
                :stroke-color="getHealthScoreColor(record.healthScore)"
              />
              <div style="margin-top: 4px; font-size: 12px; text-align: center;">
                {{ record.healthScore }}分
              </div>
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button size="small" type="primary" @click="editNutrition(record)">
                编辑
              </a-button>
              <a-button size="small" @click="viewNutritionDetail(record)">
                详情
              </a-button>
              <a-dropdown>
                <a-button size="small">
                  更多
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleMoreAction">
                    <a-menu-item :key="`analysis-${record.id}`">
                      <bar-chart-outlined />
                      营养分析
                    </a-menu-item>
                    <a-menu-item :key="`compare-${record.id}`">
                      <diff-outlined />
                      营养对比
                    </a-menu-item>
                    <a-menu-item :key="`recommend-${record.id}`">
                      <star-outlined />
                      推荐搭配
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item :key="`delete-${record.id}`" danger>
                      <delete-outlined />
                      删除记录
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑营养信息弹窗 -->
    <a-modal
      v-model:open="nutritionModalVisible"
      :title="isEditing ? '编辑营养信息' : '添加营养信息'"
      width="800px"
      @ok="saveNutrition"
      @cancel="closeNutritionModal"
    >
      <a-form :model="nutritionForm" :rules="nutritionFormRules" layout="vertical" ref="nutritionFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="选择菜品" name="dishId">
              <a-select
                v-model:value="nutritionForm.dishId"
                placeholder="请选择菜品"
                show-search
                :filter-option="filterDishOption"
              >
                <a-select-option v-for="dish in availableDishes" :key="dish.id" :value="dish.id">
                  {{ dish.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="每份重量(g)" name="portionSize">
              <a-input-number
                v-model:value="nutritionForm.portionSize"
                :min="1"
                :max="2000"
                style="width: 100%"
                placeholder="请输入每份重量"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider>基础营养成分 (每100g)</a-divider>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="热量(kcal)" name="calories">
              <a-input-number
                v-model:value="nutritionForm.calories"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="热量"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="蛋白质(g)" name="protein">
              <a-input-number
                v-model:value="nutritionForm.protein"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="蛋白质"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="脂肪(g)" name="fat">
              <a-input-number
                v-model:value="nutritionForm.fat"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="脂肪"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="碳水化合物(g)" name="carbohydrate">
              <a-input-number
                v-model:value="nutritionForm.carbohydrate"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="碳水化合物"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="膳食纤维(g)" name="fiber">
              <a-input-number
                v-model:value="nutritionForm.fiber"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="膳食纤维"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="钠(mg)" name="sodium">
              <a-input-number
                v-model:value="nutritionForm.sodium"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="钠"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="胆固醇(mg)" name="cholesterol">
              <a-input-number
                v-model:value="nutritionForm.cholesterol"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="胆固醇"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="糖分(g)" name="sugar">
              <a-input-number
                v-model:value="nutritionForm.sugar"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="糖分"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider>维生素含量</a-divider>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="维生素A(μg)">
              <a-input-number
                v-model:value="nutritionForm.vitaminA"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="维生素A"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="维生素C(mg)">
              <a-input-number
                v-model:value="nutritionForm.vitaminC"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="维生素C"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="维生素E(mg)">
              <a-input-number
                v-model:value="nutritionForm.vitaminE"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="维生素E"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider>矿物质含量</a-divider>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="钙(mg)">
              <a-input-number
                v-model:value="nutritionForm.calcium"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="钙"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="铁(mg)">
              <a-input-number
                v-model:value="nutritionForm.iron"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="铁"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="锌(mg)">
              <a-input-number
                v-model:value="nutritionForm.zinc"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="锌"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="钾(mg)">
              <a-input-number
                v-model:value="nutritionForm.potassium"
                :min="0"
                :precision="1"
                style="width: 100%"
                placeholder="钾"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="营养标签">
          <a-select
            v-model:value="nutritionForm.nutritionTags"
            mode="tags"
            placeholder="请选择或输入营养标签"
            :options="nutritionTagOptions"
          />
        </a-form-item>

        <a-form-item label="营养备注">
          <a-textarea
            v-model:value="nutritionForm.nutritionNotes"
            :rows="3"
            placeholder="请输入营养成分备注信息"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 营养详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      :title="`${currentNutrition?.dishName} - 营养详情`"
      width="1000px"
      @ok="detailModalVisible = false"
    >
      <div v-if="currentNutrition">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="营养成分雷达图" size="small">
              <div id="nutrition-radar-chart" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="营养成分分布" size="small">
              <div id="nutrition-pie-chart" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
        <a-card title="详细营养信息" size="small" style="margin-top: 16px;">
          <a-descriptions :column="3" bordered size="small">
            <a-descriptions-item label="热量">{{ currentNutrition.calories }} kcal/100g</a-descriptions-item>
            <a-descriptions-item label="蛋白质">{{ currentNutrition.protein }}g</a-descriptions-item>
            <a-descriptions-item label="脂肪">{{ currentNutrition.fat }}g</a-descriptions-item>
            <a-descriptions-item label="碳水化合物">{{ currentNutrition.carbohydrate }}g</a-descriptions-item>
            <a-descriptions-item label="膳食纤维">{{ currentNutrition.fiber }}g</a-descriptions-item>
            <a-descriptions-item label="钠">{{ currentNutrition.sodium }}mg</a-descriptions-item>
            <a-descriptions-item label="维生素A" v-for="vitamin in currentNutrition.vitamins" :key="vitamin.name">
              {{ vitamin.name }}: {{ vitamin.value }}{{ vitamin.unit }}
            </a-descriptions-item>
            <a-descriptions-item label="矿物质" :span="3">
              <a-space>
                <a-tag v-for="mineral in currentNutrition.minerals" :key="mineral.name">
                  {{ mineral.name }}: {{ mineral.value }}{{ mineral.unit }}
                </a-tag>
              </a-space>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>
    </a-modal>

    <!-- 营养分析弹窗 -->
    <a-modal
      v-model:open="analysisModalVisible"
      title="营养分析报告"
      width="1200px"
      @ok="analysisModalVisible = false"
    >
      <a-tabs>
        <a-tab-pane key="overview" tab="营养概览">
          <div id="nutrition-overview-chart" style="height: 400px;"></div>
        </a-tab-pane>
        <a-tab-pane key="comparison" tab="营养对比">
          <div id="nutrition-comparison-chart" style="height: 400px;"></div>
        </a-tab-pane>
        <a-tab-pane key="trends" tab="营养趋势">
          <div id="nutrition-trends-chart" style="height: 400px;"></div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  PlusOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  HeartOutlined,
  FireOutlined,
  SearchOutlined,
  ReloadOutlined,
  BarChartOutlined,
  DownOutlined,
  DiffOutlined,
  StarOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DishesNutrition',
  components: {
    PlusOutlined,
    ExperimentOutlined,
    FileTextOutlined,
    UploadOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    HeartOutlined,
    FireOutlined,
    SearchOutlined,
    ReloadOutlined,
    BarChartOutlined,
    DownOutlined,
    DiffOutlined,
    StarOutlined,
    DeleteOutlined
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const nutritionModalVisible = ref(false)
    const detailModalVisible = ref(false)
    const analysisModalVisible = ref(false)
    const isEditing = ref(false)
    const nutritionFormRef = ref()
    const currentNutrition = ref(null)

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      category: '',
      nutritionLevel: '',
      caloriesRange: null
    })

    // 营养统计
    const nutritionStats = reactive({
      recorded: 142,
      pending: 44,
      highNutrition: 67,
      avgCalories: 285
    })

    // 营养表单
    const nutritionForm = reactive({
      id: null,
      dishId: null,
      portionSize: 100,
      calories: null,
      protein: null,
      fat: null,
      carbohydrate: null,
      fiber: null,
      sodium: null,
      cholesterol: null,
      sugar: null,
      vitaminA: null,
      vitaminC: null,
      vitaminE: null,
      calcium: null,
      iron: null,
      zinc: null,
      potassium: null,
      nutritionTags: [],
      nutritionNotes: ''
    })

    // 营养标签选项
    const nutritionTagOptions = ref([
      { label: '高蛋白', value: '高蛋白' },
      { label: '低脂肪', value: '低脂肪' },
      { label: '低热量', value: '低热量' },
      { label: '高纤维', value: '高纤维' },
      { label: '低钠', value: '低钠' },
      { label: '富含维生素', value: '富含维生素' },
      { label: '富含矿物质', value: '富含矿物质' },
      { label: '均衡营养', value: '均衡营养' }
    ])

    // 表单验证规则
    const nutritionFormRules = {
      dishId: [{ required: true, message: '请选择菜品', trigger: 'change' }],
      portionSize: [{ required: true, message: '请输入每份重量', trigger: 'blur' }],
      calories: [{ required: true, message: '请输入热量', trigger: 'blur' }],
      protein: [{ required: true, message: '请输入蛋白质含量', trigger: 'blur' }],
      fat: [{ required: true, message: '请输入脂肪含量', trigger: 'blur' }],
      carbohydrate: [{ required: true, message: '请输入碳水化合物含量', trigger: 'blur' }]
    }

    // 可选菜品列表
    const availableDishes = ref([
      { id: 1, name: '宫保鸡丁', category: 'hot' },
      { id: 2, name: '麻婆豆腐', category: 'hot' },
      { id: 3, name: '糖醋里脊', category: 'hot' },
      { id: 4, name: '凉拌黄瓜', category: 'cold' },
      { id: 5, name: '番茄鸡蛋汤', category: 'soup' }
    ])

    // 营养数据
    const nutritionData = ref([
      {
        id: 1,
        dishId: 1,
        dishName: '宫保鸡丁',
        category: 'hot',
        image: '/images/dish1.jpg',
        portionSize: 200,
        calories: 285.5,
        protein: 18.2,
        fat: 15.8,
        carbohydrate: 12.5,
        fiber: 2.8,
        sodium: 856,
        cholesterol: 65,
        sugar: 8.2,
        vitamins: [
          { name: '维生素A', value: 125, unit: 'μg' },
          { name: '维生素C', value: 15, unit: 'mg' },
          { name: '维生素E', value: 3.2, unit: 'mg' }
        ],
        minerals: [
          { name: '钙', value: 45, unit: 'mg' },
          { name: '铁', value: 2.8, unit: 'mg' },
          { name: '锌', value: 1.5, unit: 'mg' },
          { name: '钾', value: 285, unit: 'mg' }
        ],
        nutritionLevel: 'high',
        healthScore: 85,
        nutritionTags: ['高蛋白', '均衡营养'],
        nutritionNotes: '营养均衡，蛋白质含量丰富',
        createdAt: '2024-01-10 10:30:00',
        updatedAt: '2024-01-15 14:20:00'
      },
      {
        id: 2,
        dishId: 2,
        dishName: '麻婆豆腐',
        category: 'hot',
        image: '/images/dish2.jpg',
        portionSize: 180,
        calories: 145.2,
        protein: 12.5,
        fat: 8.9,
        carbohydrate: 6.8,
        fiber: 3.2,
        sodium: 698,
        cholesterol: 18,
        sugar: 4.5,
        vitamins: [
          { name: '维生素A', value: 85, unit: 'μg' },
          { name: '维生素C', value: 8, unit: 'mg' },
          { name: '维生素E', value: 2.1, unit: 'mg' }
        ],
        minerals: [
          { name: '钙', value: 168, unit: 'mg' },
          { name: '铁', value: 3.2, unit: 'mg' },
          { name: '锌', value: 1.8, unit: 'mg' },
          { name: '钾', value: 195, unit: 'mg' }
        ],
        nutritionLevel: 'medium',
        healthScore: 78,
        nutritionTags: ['高蛋白', '富含矿物质', '低热量'],
        nutritionNotes: '豆制品，富含植物蛋白',
        createdAt: '2024-01-08 09:15:00',
        updatedAt: '2024-01-14 16:45:00'
      },
      {
        id: 3,
        dishId: 4,
        dishName: '凉拌黄瓜',
        category: 'cold',
        image: '/images/dish4.jpg',
        portionSize: 150,
        calories: 25.8,
        protein: 1.2,
        fat: 0.8,
        carbohydrate: 4.5,
        fiber: 2.1,
        sodium: 125,
        cholesterol: 0,
        sugar: 3.2,
        vitamins: [
          { name: '维生素A', value: 45, unit: 'μg' },
          { name: '维生素C', value: 28, unit: 'mg' },
          { name: '维生素E', value: 0.8, unit: 'mg' }
        ],
        minerals: [
          { name: '钙', value: 28, unit: 'mg' },
          { name: '铁', value: 0.5, unit: 'mg' },
          { name: '锌', value: 0.3, unit: 'mg' },
          { name: '钾', value: 158, unit: 'mg' }
        ],
        nutritionLevel: 'low',
        healthScore: 92,
        nutritionTags: ['低热量', '高纤维', '富含维生素'],
        nutritionNotes: '清淡爽口，富含维生素C',
        createdAt: '2024-01-12 08:00:00',
        updatedAt: '2024-01-15 09:20:00'
      }
    ])

    // 表格列配置
    const nutritionColumns = [
      {
        title: '菜品信息',
        dataIndex: 'dishInfo',
        key: 'dishInfo',
        width: 200,
        fixed: 'left'
      },
      {
        title: '热量',
        dataIndex: 'calories',
        key: 'calories',
        width: 100,
        sorter: (a, b) => a.calories - b.calories
      },
      {
        title: '三大营养素',
        dataIndex: 'macronutrients',
        key: 'macronutrients',
        width: 180
      },
      {
        title: '维生素',
        dataIndex: 'vitamins',
        key: 'vitamins',
        width: 150
      },
      {
        title: '矿物质',
        dataIndex: 'minerals',
        key: 'minerals',
        width: 150
      },
      {
        title: '营养等级',
        dataIndex: 'nutritionLevel',
        key: 'nutritionLevel',
        width: 100
      },
      {
        title: '健康评分',
        dataIndex: 'healthScore',
        key: 'healthScore',
        width: 120,
        sorter: (a, b) => a.healthScore - b.healthScore
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        width: 150
      },
      {
        title: '操作',
        key: 'action',
        width: 180,
        fixed: 'right'
      }
    ]

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    })

    // 过滤后的营养数据
    const filteredNutrition = computed(() => {
      let filtered = nutritionData.value

      if (searchForm.keyword) {
        filtered = filtered.filter(item => 
          item.dishName.includes(searchForm.keyword)
        )
      }

      if (searchForm.category) {
        filtered = filtered.filter(item => item.category === searchForm.category)
      }

      if (searchForm.nutritionLevel) {
        filtered = filtered.filter(item => item.nutritionLevel === searchForm.nutritionLevel)
      }

      if (searchForm.caloriesRange && searchForm.caloriesRange.length === 2) {
        const [minCalories, maxCalories] = searchForm.caloriesRange
        filtered = filtered.filter(item => 
          item.calories >= parseFloat(minCalories) && item.calories <= parseFloat(maxCalories)
        )
      }

      pagination.total = filtered.length
      return filtered
    })

    // 获取分类颜色
    const getCategoryColor = (category) => {
      const colors = {
        hot: 'red',
        cold: 'blue',
        soup: 'green',
        staple: 'orange'
      }
      return colors[category] || 'default'
    }

    // 获取分类文本
    const getCategoryText = (category) => {
      const texts = {
        hot: '热菜',
        cold: '凉菜',
        soup: '汤类',
        staple: '主食'
      }
      return texts[category] || category
    }

    // 获取营养等级颜色
    const getNutritionLevelColor = (level) => {
      const colors = {
        high: 'green',
        medium: 'orange',
        low: 'red'
      }
      return colors[level] || 'default'
    }

    // 获取营养等级文本
    const getNutritionLevelText = (level) => {
      const texts = {
        high: '高营养',
        medium: '中营养',
        low: '低营养'
      }
      return texts[level] || level
    }

    // 获取健康评分颜色
    const getHealthScoreColor = (score) => {
      if (score >= 80) return '#52c41a'
      if (score >= 60) return '#faad14'
      return '#f5222d'
    }

    // 搜索功能
    const onSearch = () => {
      message.success('搜索完成')
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        keyword: '',
        category: '',
        nutritionLevel: '',
        caloriesRange: null
      })
      message.success('搜索条件已重置')
    }

    // 过滤菜品选项
    const filterDishOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 添加营养信息
    const addNutrition = () => {
      isEditing.value = false
      resetNutritionForm()
      nutritionModalVisible.value = true
    }

    // 编辑营养信息
    const editNutrition = (record) => {
      isEditing.value = true
      Object.assign(nutritionForm, {
        ...record,
        vitaminA: record.vitamins.find(v => v.name === '维生素A')?.value || null,
        vitaminC: record.vitamins.find(v => v.name === '维生素C')?.value || null,
        vitaminE: record.vitamins.find(v => v.name === '维生素E')?.value || null,
        calcium: record.minerals.find(m => m.name === '钙')?.value || null,
        iron: record.minerals.find(m => m.name === '铁')?.value || null,
        zinc: record.minerals.find(m => m.name === '锌')?.value || null,
        potassium: record.minerals.find(m => m.name === '钾')?.value || null
      })
      nutritionModalVisible.value = true
    }

    // 重置营养表单
    const resetNutritionForm = () => {
      Object.assign(nutritionForm, {
        id: null,
        dishId: null,
        portionSize: 100,
        calories: null,
        protein: null,
        fat: null,
        carbohydrate: null,
        fiber: null,
        sodium: null,
        cholesterol: null,
        sugar: null,
        vitaminA: null,
        vitaminC: null,
        vitaminE: null,
        calcium: null,
        iron: null,
        zinc: null,
        potassium: null,
        nutritionTags: [],
        nutritionNotes: ''
      })
    }

    // 关闭营养弹窗
    const closeNutritionModal = () => {
      nutritionModalVisible.value = false
      resetNutritionForm()
    }

    // 保存营养信息
    const saveNutrition = async () => {
      try {
        await nutritionFormRef.value.validate()
        
        const dish = availableDishes.value.find(d => d.id === nutritionForm.dishId)
        const vitamins = [
          { name: '维生素A', value: nutritionForm.vitaminA || 0, unit: 'μg' },
          { name: '维生素C', value: nutritionForm.vitaminC || 0, unit: 'mg' },
          { name: '维生素E', value: nutritionForm.vitaminE || 0, unit: 'mg' }
        ]
        const minerals = [
          { name: '钙', value: nutritionForm.calcium || 0, unit: 'mg' },
          { name: '铁', value: nutritionForm.iron || 0, unit: 'mg' },
          { name: '锌', value: nutritionForm.zinc || 0, unit: 'mg' },
          { name: '钾', value: nutritionForm.potassium || 0, unit: 'mg' }
        ]

        // 计算营养等级和健康评分
        const nutritionLevel = calculateNutritionLevel(nutritionForm)
        const healthScore = calculateHealthScore(nutritionForm)

        if (isEditing.value) {
          // 更新营养信息
          const index = nutritionData.value.findIndex(item => item.id === nutritionForm.id)
          if (index !== -1) {
            nutritionData.value[index] = {
              ...nutritionForm,
              dishName: dish.name,
              category: dish.category,
              image: `/images/dish${dish.id}.jpg`,
              vitamins,
              minerals,
              nutritionLevel,
              healthScore,
              updatedAt: new Date().toLocaleString()
            }
          }
          message.success('营养信息更新成功')
        } else {
          // 添加营养信息
          const newNutrition = {
            ...nutritionForm,
            id: Date.now(),
            dishName: dish.name,
            category: dish.category,
            image: `/images/dish${dish.id}.jpg`,
            vitamins,
            minerals,
            nutritionLevel,
            healthScore,
            createdAt: new Date().toLocaleString(),
            updatedAt: new Date().toLocaleString()
          }
          nutritionData.value.unshift(newNutrition)
          nutritionStats.recorded++
          if (nutritionStats.pending > 0) {
            nutritionStats.pending--
          }
          message.success('营养信息添加成功')
        }
        
        closeNutritionModal()
      } catch (error) {
        message.error('请检查表单信息')
      }
    }

    // 计算营养等级
    const calculateNutritionLevel = (form) => {
      const score = (form.protein * 4 + form.vitaminC * 2 + form.calcium * 0.1) / 10
      if (score >= 8) return 'high'
      if (score >= 5) return 'medium'
      return 'low'
    }

    // 计算健康评分
    const calculateHealthScore = (form) => {
      let score = 50
      if (form.protein > 15) score += 15
      if (form.fat < 10) score += 10
      if (form.fiber > 2) score += 10
      if (form.sodium < 500) score += 10
      if (form.vitaminC > 20) score += 5
      return Math.min(score, 100)
    }

    // 查看营养详情
    const viewNutritionDetail = (record) => {
      currentNutrition.value = record
      detailModalVisible.value = true
      nextTick(() => {
        initNutritionCharts(record)
      })
    }

    // 初始化营养图表
    const initNutritionCharts = (nutrition) => {
      // 雷达图
      const radarDom = document.getElementById('nutrition-radar-chart')
      if (radarDom) {
        const radarChart = echarts.init(radarDom)
        const radarOption = {
          radar: {
            indicator: [
              { name: '蛋白质', max: 30 },
              { name: '脂肪', max: 20 },
              { name: '碳水', max: 50 },
              { name: '纤维', max: 10 },
              { name: '维C', max: 100 },
              { name: '钙', max: 200 }
            ]
          },
          series: [{
            type: 'radar',
            data: [{
              value: [
                nutrition.protein,
                nutrition.fat,
                nutrition.carbohydrate,
                nutrition.fiber,
                nutrition.vitamins.find(v => v.name === '维生素C')?.value || 0,
                nutrition.minerals.find(m => m.name === '钙')?.value || 0
              ],
              name: '营养成分'
            }]
          }]
        }
        radarChart.setOption(radarOption)
      }

      // 饼图
      const pieDom = document.getElementById('nutrition-pie-chart')
      if (pieDom) {
        const pieChart = echarts.init(pieDom)
        const pieOption = {
          series: [{
            type: 'pie',
            radius: '50%',
            data: [
              { value: nutrition.protein * 4, name: '蛋白质' },
              { value: nutrition.fat * 9, name: '脂肪' },
              { value: nutrition.carbohydrate * 4, name: '碳水化合物' }
            ]
          }]
        }
        pieChart.setOption(pieOption)
      }
    }

    // 营养分析
    const showNutritionAnalysis = () => {
      analysisModalVisible.value = true
      nextTick(() => {
        initAnalysisCharts()
      })
    }

    // 初始化分析图表
    const initAnalysisCharts = () => {
      // 概览图表
      const overviewDom = document.getElementById('nutrition-overview-chart')
      if (overviewDom) {
        const overviewChart = echarts.init(overviewDom)
        const overviewOption = {
          xAxis: {
            type: 'category',
            data: nutritionData.value.map(item => item.dishName)
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            data: nutritionData.value.map(item => item.calories),
            type: 'bar'
          }]
        }
        overviewChart.setOption(overviewOption)
      }
    }

    // 批量分析
    const batchAnalysis = () => {
      message.info('批量营养分析功能')
    }

    // 营养报告
    const nutritionReport = () => {
      message.success('营养报告生成成功')
    }

    // 导入营养数据
    const importNutrition = () => {
      message.info('导入营养数据功能')
    }

    // 刷新数据
    const refreshData = () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        message.success('数据刷新成功')
      }, 1000)
    }

    // 处理更多操作
    const handleMoreAction = ({ key }) => {
      const [action, id] = key.split('-')
      const nutrition = nutritionData.value.find(n => n.id === parseInt(id))
      
      switch (action) {
        case 'analysis':
          message.info(`营养分析: ${nutrition.dishName}`)
          break
        case 'compare':
          message.info(`营养对比: ${nutrition.dishName}`)
          break
        case 'recommend':
          message.info(`推荐搭配: ${nutrition.dishName}`)
          break
        case 'delete':
          const index = nutritionData.value.findIndex(n => n.id === parseInt(id))
          if (index !== -1) {
            nutritionData.value.splice(index, 1)
            nutritionStats.recorded--
            nutritionStats.pending++
            message.success(`营养记录删除成功: ${nutrition.dishName}`)
          }
          break
      }
    }

    onMounted(() => {
      // 初始化数据
    })

    return {
      loading,
      nutritionModalVisible,
      detailModalVisible,
      analysisModalVisible,
      isEditing,
      nutritionFormRef,
      currentNutrition,
      searchForm,
      nutritionStats,
      nutritionForm,
      nutritionTagOptions,
      nutritionFormRules,
      availableDishes,
      nutritionData,
      nutritionColumns,
      pagination,
      filteredNutrition,
      getCategoryColor,
      getCategoryText,
      getNutritionLevelColor,
      getNutritionLevelText,
      getHealthScoreColor,
      onSearch,
      resetSearch,
      filterDishOption,
      addNutrition,
      editNutrition,
      closeNutritionModal,
      saveNutrition,
      viewNutritionDetail,
      showNutritionAnalysis,
      batchAnalysis,
      nutritionReport,
      importNutrition,
      refreshData,
      handleMoreAction
    }
  }
}
</script>

<style scoped>
.stat-card {
  transition: all 0.3s;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.dish-info {
  display: flex;
  align-items: center;
}

.calories-info {
  text-align: center;
}

.calories-value {
  font-size: 16px;
  font-weight: bold;
  color: #fa541c;
}

.calories-unit {
  font-size: 12px;
  color: #666;
}

.macro-nutrients {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nutrient-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.nutrient-label {
  color: #666;
}

.nutrient-value {
  font-weight: bold;
}

.health-score {
  text-align: center;
}
</style>