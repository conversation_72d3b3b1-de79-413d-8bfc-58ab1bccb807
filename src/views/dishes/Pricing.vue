<template>
  <div class="dishes-pricing">
    <div class="page-title">价格管理</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button type="primary" @click="batchPriceUpdate">
        <edit-outlined />
        批量调价
      </a-button>
      <a-button @click="priceStrategy">
        <strategy-outlined />
        定价策略
      </a-button>
      <a-button @click="priceHistory">
        <history-outlined />
        价格历史
      </a-button>
      <a-button @click="exportPricing">
        <download-outlined />
        导出价格表
      </a-button>
    </div>

    <!-- 价格统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="平均菜品价格"
            :value="pricingStats.avgPrice"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <dollar-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="最高价格"
            :value="pricingStats.maxPrice"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#fa541c' }"
          >
            <template #prefix>
              <arrow-up-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="最低价格"
            :value="pricingStats.minPrice"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <arrow-down-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="6">
        <a-card class="stat-card">
          <a-statistic
            title="本月调价次数"
            :value="pricingStats.monthlyChanges"
            suffix="次"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <swap-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区 -->
    <a-card style="margin-bottom: 24px;">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="6">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索菜品名称"
            allow-clear
            @change="onSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-select
            v-model:value="searchForm.category"
            placeholder="菜品分类"
            allow-clear
            @change="onSearch"
          >
            <a-select-option value="">全部分类</a-select-option>
            <a-select-option value="hot">热菜</a-select-option>
            <a-select-option value="cold">凉菜</a-select-option>
            <a-select-option value="soup">汤类</a-select-option>
            <a-select-option value="staple">主食</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-select
            v-model:value="searchForm.priceLevel"
            placeholder="价格区间"
            allow-clear
            @change="onSearch"
          >
            <a-select-option value="">全部价格</a-select-option>
            <a-select-option value="low">低价(≤15元)</a-select-option>
            <a-select-option value="medium">中价(16-30元)</a-select-option>
            <a-select-option value="high">高价(>30元)</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="6">
          <a-range-picker
            v-model:value="searchForm.priceRange"
            placeholder="['最低价格', '最高价格']"
            @change="onSearch"
          />
        </a-col>
        <a-col :xs="24" :sm="4">
          <a-space>
            <a-button type="primary" @click="onSearch">
              <search-outlined />
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <reload-outlined />
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 价格管理表格 -->
    <a-card title="价格管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-tooltip title="刷新价格">
            <a-button @click="refreshPricing">
              <reload-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="价格分析">
            <a-button @click="showPriceAnalysis">
              <bar-chart-outlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>

      <a-table
        :columns="pricingColumns"
        :data-source="filteredPricing"
        :row-selection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        :scroll="{ x: 1600 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'dishInfo'">
            <div class="dish-info">
              <a-avatar :src="record.image" size="small" style="margin-right: 8px;" />
              <div>
                <a-typography-text strong>{{ record.dishName }}</a-typography-text>
                <br />
                <a-tag :color="getCategoryColor(record.category)" size="small">
                  {{ getCategoryText(record.category) }}
                </a-tag>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'currentPrice'">
            <div class="price-info">
              <a-typography-text strong type="danger" style="font-size: 16px;">
                ¥{{ record.currentPrice }}
              </a-typography-text>
              <div v-if="record.originalPrice !== record.currentPrice" class="original-price">
                原价: ¥{{ record.originalPrice }}
              </div>
            </div>
          </template>
          <template v-if="column.key === 'priceChange'">
            <div v-if="record.priceChangePercent !== 0" class="price-change">
              <a-tag :color="record.priceChangePercent > 0 ? 'red' : 'green'">
                <template #icon>
                  <component :is="record.priceChangePercent > 0 ? 'ArrowUpOutlined' : 'ArrowDownOutlined'" />
                </template>
                {{ Math.abs(record.priceChangePercent) }}%
              </a-tag>
              <div style="font-size: 12px; margin-top: 2px;">
                {{ record.priceChangePercent > 0 ? '+' : '' }}¥{{ record.priceChangeAmount }}
              </div>
            </div>
            <span v-else>-</span>
          </template>
          <template v-if="column.key === 'costInfo'">
            <div class="cost-info">
              <div>成本: ¥{{ record.cost }}</div>
              <div>毛利: {{ record.profitMargin }}%</div>
              <div>毛利额: ¥{{ (record.currentPrice - record.cost).toFixed(2) }}</div>
            </div>
          </template>
          <template v-if="column.key === 'salesData'">
            <div class="sales-data">
              <div>销量: {{ record.salesVolume }}份</div>
              <div>收入: ¥{{ record.revenue }}</div>
              <a-progress
                :percent="record.popularityIndex"
                size="small"
                :stroke-color="getPopularityColor(record.popularityIndex)"
              />
              <div style="font-size: 12px;">热度: {{ record.popularityIndex }}%</div>
            </div>
          </template>
          <template v-if="column.key === 'priceStrategy'">
            <a-tag :color="getStrategyColor(record.strategy)">
              {{ getStrategyText(record.strategy) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button size="small" type="primary" @click="adjustPrice(record)">
                调价
              </a-button>
              <a-dropdown>
                <a-button size="small">
                  更多
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="handleMoreAction">
                    <a-menu-item :key="`history-${record.id}`">
                      <history-outlined />
                      价格历史
                    </a-menu-item>
                    <a-menu-item :key="`analysis-${record.id}`">
                      <bar-chart-outlined />
                      价格分析
                    </a-menu-item>
                    <a-menu-item :key="`copy-${record.id}`">
                      <copy-outlined />
                      复制定价
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item :key="`reset-${record.id}`">
                      <reload-outlined />
                      重置价格
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 调价弹窗 -->
    <a-modal
      v-model:open="priceModalVisible"
      :title="`调整价格 - ${currentDish?.dishName}`"
      width="600px"
      @ok="savePriceAdjustment"
      @cancel="closePriceModal"
    >
      <a-form :model="priceForm" :rules="priceFormRules" layout="vertical" ref="priceFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="当前价格" name="currentPrice">
              <a-input-number
                v-model:value="priceForm.currentPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
                disabled
                addon-before="¥"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="新价格" name="newPrice">
              <a-input-number
                v-model:value="priceForm.newPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入新价格"
                addon-before="¥"
                @change="calculatePriceChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="调价类型" name="adjustmentType">
              <a-radio-group v-model:value="priceForm.adjustmentType">
                <a-radio value="increase">涨价</a-radio>
                <a-radio value="decrease">降价</a-radio>
                <a-radio value="promotion">促销</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生效时间" name="effectiveDate">
              <a-date-picker
                v-model:value="priceForm.effectiveDate"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="选择生效时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="调价原因" name="reason">
          <a-select v-model:value="priceForm.reason" placeholder="请选择调价原因">
            <a-select-option value="cost_increase">成本上涨</a-select-option>
            <a-select-option value="cost_decrease">成本下降</a-select-option>
            <a-select-option value="market_competition">市场竞争</a-select-option>
            <a-select-option value="seasonal_adjustment">季节调整</a-select-option>
            <a-select-option value="promotion_activity">促销活动</a-select-option>
            <a-select-option value="profit_optimization">利润优化</a-select-option>
            <a-select-option value="other">其他原因</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="调价说明" name="notes">
          <a-textarea
            v-model:value="priceForm.notes"
            :rows="3"
            placeholder="请输入调价说明"
          />
        </a-form-item>
        <a-alert
          v-if="priceChangeInfo.show"
          :message="priceChangeInfo.message"
          :type="priceChangeInfo.type"
          show-icon
          style="margin-top: 16px;"
        />
      </a-form>
    </a-modal>

    <!-- 批量调价弹窗 -->
    <a-modal
      v-model:open="batchPriceModalVisible"
      title="批量调价"
      width="700px"
      @ok="executeBatchPriceUpdate"
      @cancel="batchPriceModalVisible = false"
    >
      <p>已选择 <strong>{{ selectedRowKeys.length }}</strong> 个菜品</p>
      <a-form :model="batchPriceForm" layout="vertical">
        <a-form-item label="调价方式">
          <a-radio-group v-model:value="batchPriceForm.method">
            <a-radio value="percentage">百分比调整</a-radio>
            <a-radio value="fixed">固定金额调整</a-radio>
            <a-radio value="set">设定新价格</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="batchPriceForm.method === 'percentage'" label="调整百分比">
          <a-input-number
            v-model:value="batchPriceForm.percentage"
            :precision="1"
            placeholder="输入百分比"
            style="width: 100%"
            addon-after="%"
          />
          <div style="margin-top: 8px; color: #666;">
            正数表示涨价，负数表示降价
          </div>
        </a-form-item>
        <a-form-item v-if="batchPriceForm.method === 'fixed'" label="调整金额">
          <a-input-number
            v-model:value="batchPriceForm.amount"
            :precision="2"
            placeholder="输入调整金额"
            style="width: 100%"
            addon-before="¥"
          />
          <div style="margin-top: 8px; color: #666;">
            正数表示涨价，负数表示降价
          </div>
        </a-form-item>
        <a-form-item v-if="batchPriceForm.method === 'set'" label="新价格">
          <a-input-number
            v-model:value="batchPriceForm.newPrice"
            :precision="2"
            :min="0"
            placeholder="输入新价格"
            style="width: 100%"
            addon-before="¥"
          />
        </a-form-item>
        <a-form-item label="生效时间">
          <a-date-picker
            v-model:value="batchPriceForm.effectiveDate"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择生效时间"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="调价原因">
          <a-select v-model:value="batchPriceForm.reason" placeholder="请选择调价原因">
            <a-select-option value="cost_increase">成本上涨</a-select-option>
            <a-select-option value="market_competition">市场竞争</a-select-option>
            <a-select-option value="seasonal_adjustment">季节调整</a-select-option>
            <a-select-option value="promotion_activity">促销活动</a-select-option>
            <a-select-option value="profit_optimization">利润优化</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 价格分析弹窗 -->
    <a-modal
      v-model:open="analysisModalVisible"
      title="价格分析"
      width="1200px"
      @ok="analysisModalVisible = false"
    >
      <a-tabs>
        <a-tab-pane key="price-trend" tab="价格趋势">
          <div id="price-trend-chart" style="height: 400px;"></div>
        </a-tab-pane>
        <a-tab-pane key="price-distribution" tab="价格分布">
          <div id="price-distribution-chart" style="height: 400px;"></div>
        </a-tab-pane>
        <a-tab-pane key="profit-analysis" tab="利润分析">
          <div id="profit-analysis-chart" style="height: 400px;"></div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 定价策略弹窗 -->
    <a-modal
      v-model:open="strategyModalVisible"
      title="定价策略配置"
      width="800px"
      @ok="saveStrategy"
      @cancel="strategyModalVisible = false"
    >
      <a-form :model="strategyForm" layout="vertical">
        <a-form-item label="策略名称">
          <a-input v-model:value="strategyForm.name" placeholder="请输入策略名称" />
        </a-form-item>
        <a-form-item label="定价规则">
          <a-checkbox-group v-model:value="strategyForm.rules">
            <a-checkbox value="cost_plus">成本加成定价</a-checkbox>
            <a-checkbox value="market_based">市场导向定价</a-checkbox>
            <a-checkbox value="value_based">价值导向定价</a-checkbox>
            <a-checkbox value="competitive">竞争定价</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="毛利率目标">
          <a-slider
            v-model:value="strategyForm.targetMargin"
            :min="10"
            :max="80"
            :marks="{ 10: '10%', 30: '30%', 50: '50%', 80: '80%' }"
          />
        </a-form-item>
        <a-form-item label="价格调整频率">
          <a-select v-model:value="strategyForm.adjustmentFrequency">
            <a-select-option value="daily">每日</a-select-option>
            <a-select-option value="weekly">每周</a-select-option>
            <a-select-option value="monthly">每月</a-select-option>
            <a-select-option value="quarterly">每季度</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="自动调价">
          <a-switch v-model:checked="strategyForm.autoAdjustment" />
          <span style="margin-left: 8px;">启用自动调价</span>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  EditOutlined,
  StrategyOutlined,
  HistoryOutlined,
  DownloadOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SwapOutlined,
  SearchOutlined,
  ReloadOutlined,
  BarChartOutlined,
  DownOutlined,
  CopyOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DishesPricing',
  components: {
    EditOutlined,
    StrategyOutlined,
    HistoryOutlined,
    DownloadOutlined,
    DollarOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    SwapOutlined,
    SearchOutlined,
    ReloadOutlined,
    BarChartOutlined,
    DownOutlined,
    CopyOutlined
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const priceModalVisible = ref(false)
    const batchPriceModalVisible = ref(false)
    const analysisModalVisible = ref(false)
    const strategyModalVisible = ref(false)
    const selectedRowKeys = ref([])
    const priceFormRef = ref()
    const currentDish = ref(null)

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      category: '',
      priceLevel: '',
      priceRange: null
    })

    // 价格统计
    const pricingStats = reactive({
      avgPrice: 18.65,
      maxPrice: 38.00,
      minPrice: 6.50,
      monthlyChanges: 15
    })

    // 调价表单
    const priceForm = reactive({
      currentPrice: null,
      newPrice: null,
      adjustmentType: 'increase',
      effectiveDate: dayjs(),
      reason: '',
      notes: ''
    })

    // 批量调价表单
    const batchPriceForm = reactive({
      method: 'percentage',
      percentage: null,
      amount: null,
      newPrice: null,
      effectiveDate: dayjs(),
      reason: ''
    })

    // 策略表单
    const strategyForm = reactive({
      name: '',
      rules: ['cost_plus'],
      targetMargin: 30,
      adjustmentFrequency: 'weekly',
      autoAdjustment: false
    })

    // 价格变动信息
    const priceChangeInfo = reactive({
      show: false,
      message: '',
      type: 'info'
    })

    // 表单验证规则
    const priceFormRules = {
      newPrice: [{ required: true, message: '请输入新价格', trigger: 'blur' }],
      reason: [{ required: true, message: '请选择调价原因', trigger: 'change' }]
    }

    // 价格数据
    const pricingData = ref([
      {
        id: 1,
        dishName: '宫保鸡丁',
        category: 'hot',
        image: '/images/dish1.jpg',
        currentPrice: 18.00,
        originalPrice: 16.00,
        cost: 8.50,
        profitMargin: 52.8,
        priceChangePercent: 12.5,
        priceChangeAmount: 2.00,
        salesVolume: 156,
        revenue: 2808,
        popularityIndex: 85,
        strategy: 'cost_plus',
        lastAdjustment: '2024-01-10 10:30:00',
        priceHistory: [
          { date: '2024-01-01', price: 16.00, reason: '初始定价' },
          { date: '2024-01-10', price: 18.00, reason: '成本上涨' }
        ]
      },
      {
        id: 2,
        dishName: '麻婆豆腐',
        category: 'hot',
        image: '/images/dish2.jpg',
        currentPrice: 12.00,
        originalPrice: 12.00,
        cost: 5.20,
        profitMargin: 56.7,
        priceChangePercent: 0,
        priceChangeAmount: 0,
        salesVolume: 142,
        revenue: 1704,
        popularityIndex: 78,
        strategy: 'market_based',
        lastAdjustment: '2024-01-01 00:00:00',
        priceHistory: [
          { date: '2024-01-01', price: 12.00, reason: '初始定价' }
        ]
      },
      {
        id: 3,
        dishName: '糖醋里脊',
        category: 'hot',
        image: '/images/dish3.jpg',
        currentPrice: 22.00,
        originalPrice: 24.00,
        cost: 10.80,
        profitMargin: 50.9,
        priceChangePercent: -8.3,
        priceChangeAmount: -2.00,
        salesVolume: 128,
        revenue: 2816,
        popularityIndex: 92,
        strategy: 'competitive',
        lastAdjustment: '2024-01-05 14:20:00',
        priceHistory: [
          { date: '2024-01-01', price: 24.00, reason: '初始定价' },
          { date: '2024-01-05', price: 22.00, reason: '促销活动' }
        ]
      },
      {
        id: 4,
        dishName: '凉拌黄瓜',
        category: 'cold',
        image: '/images/dish4.jpg',
        currentPrice: 8.00,
        originalPrice: 8.00,
        cost: 2.50,
        profitMargin: 68.8,
        priceChangePercent: 0,
        priceChangeAmount: 0,
        salesVolume: 89,
        revenue: 712,
        popularityIndex: 65,
        strategy: 'value_based',
        lastAdjustment: '2024-01-01 00:00:00',
        priceHistory: [
          { date: '2024-01-01', price: 8.00, reason: '初始定价' }
        ]
      },
      {
        id: 5,
        dishName: '番茄鸡蛋汤',
        category: 'soup',
        image: '/images/dish5.jpg',
        currentPrice: 10.00,
        originalPrice: 9.00,
        cost: 3.80,
        profitMargin: 62.0,
        priceChangePercent: 11.1,
        priceChangeAmount: 1.00,
        salesVolume: 67,
        revenue: 670,
        popularityIndex: 58,
        strategy: 'cost_plus',
        lastAdjustment: '2024-01-09 16:30:00',
        priceHistory: [
          { date: '2024-01-01', price: 9.00, reason: '初始定价' },
          { date: '2024-01-09', price: 10.00, reason: '成本上涨' }
        ]
      }
    ])

    // 表格列配置
    const pricingColumns = [
      {
        title: '菜品信息',
        dataIndex: 'dishInfo',
        key: 'dishInfo',
        width: 180,
        fixed: 'left'
      },
      {
        title: '当前价格',
        dataIndex: 'currentPrice',
        key: 'currentPrice',
        width: 120,
        sorter: (a, b) => a.currentPrice - b.currentPrice
      },
      {
        title: '价格变动',
        dataIndex: 'priceChange',
        key: 'priceChange',
        width: 120
      },
      {
        title: '成本信息',
        dataIndex: 'costInfo',
        key: 'costInfo',
        width: 140
      },
      {
        title: '销售数据',
        dataIndex: 'salesData',
        key: 'salesData',
        width: 160
      },
      {
        title: '定价策略',
        dataIndex: 'priceStrategy',
        key: 'priceStrategy',
        width: 120
      },
      {
        title: '最后调价',
        dataIndex: 'lastAdjustment',
        key: 'lastAdjustment',
        width: 150,
        sorter: (a, b) => new Date(a.lastAdjustment) - new Date(b.lastAdjustment)
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right'
      }
    ]

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    })

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }

    // 过滤后的价格数据
    const filteredPricing = computed(() => {
      let filtered = pricingData.value

      if (searchForm.keyword) {
        filtered = filtered.filter(item => 
          item.dishName.includes(searchForm.keyword)
        )
      }

      if (searchForm.category) {
        filtered = filtered.filter(item => item.category === searchForm.category)
      }

      if (searchForm.priceLevel) {
        filtered = filtered.filter(item => {
          switch (searchForm.priceLevel) {
            case 'low':
              return item.currentPrice <= 15
            case 'medium':
              return item.currentPrice > 15 && item.currentPrice <= 30
            case 'high':
              return item.currentPrice > 30
            default:
              return true
          }
        })
      }

      if (searchForm.priceRange && searchForm.priceRange.length === 2) {
        const [minPrice, maxPrice] = searchForm.priceRange
        filtered = filtered.filter(item => 
          item.currentPrice >= parseFloat(minPrice) && item.currentPrice <= parseFloat(maxPrice)
        )
      }

      pagination.total = filtered.length
      return filtered
    })

    // 获取分类颜色
    const getCategoryColor = (category) => {
      const colors = {
        hot: 'red',
        cold: 'blue',
        soup: 'green',
        staple: 'orange'
      }
      return colors[category] || 'default'
    }

    // 获取分类文本
    const getCategoryText = (category) => {
      const texts = {
        hot: '热菜',
        cold: '凉菜',
        soup: '汤类',
        staple: '主食'
      }
      return texts[category] || category
    }

    // 获取热度颜色
    const getPopularityColor = (index) => {
      if (index >= 80) return '#52c41a'
      if (index >= 60) return '#faad14'
      return '#f5222d'
    }

    // 获取策略颜色
    const getStrategyColor = (strategy) => {
      const colors = {
        cost_plus: 'blue',
        market_based: 'green',
        value_based: 'orange',
        competitive: 'purple'
      }
      return colors[strategy] || 'default'
    }

    // 获取策略文本
    const getStrategyText = (strategy) => {
      const texts = {
        cost_plus: '成本加成',
        market_based: '市场导向',
        value_based: '价值导向',
        competitive: '竞争定价'
      }
      return texts[strategy] || strategy
    }

    // 搜索功能
    const onSearch = () => {
      message.success('搜索完成')
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        keyword: '',
        category: '',
        priceLevel: '',
        priceRange: null
      })
      message.success('搜索条件已重置')
    }

    // 调整价格
    const adjustPrice = (record) => {
      currentDish.value = record
      priceForm.currentPrice = record.currentPrice
      priceForm.newPrice = record.currentPrice
      priceForm.adjustmentType = 'increase'
      priceForm.effectiveDate = dayjs()
      priceForm.reason = ''
      priceForm.notes = ''
      priceChangeInfo.show = false
      priceModalVisible.value = true
    }

    // 计算价格变动
    const calculatePriceChange = () => {
      if (priceForm.newPrice && priceForm.currentPrice) {
        const change = priceForm.newPrice - priceForm.currentPrice
        const changePercent = ((change / priceForm.currentPrice) * 100).toFixed(1)
        
        if (change > 0) {
          priceChangeInfo.message = `价格上涨 ${changePercent}%，增加 ¥${change.toFixed(2)}`
          priceChangeInfo.type = 'warning'
          priceForm.adjustmentType = 'increase'
        } else if (change < 0) {
          priceChangeInfo.message = `价格下降 ${Math.abs(changePercent)}%，减少 ¥${Math.abs(change).toFixed(2)}`
          priceChangeInfo.type = 'info'
          priceForm.adjustmentType = 'decrease'
        } else {
          priceChangeInfo.message = '价格无变化'
          priceChangeInfo.type = 'success'
        }
        priceChangeInfo.show = true
      } else {
        priceChangeInfo.show = false
      }
    }

    // 关闭调价弹窗
    const closePriceModal = () => {
      priceModalVisible.value = false
      currentDish.value = null
      priceChangeInfo.show = false
    }

    // 保存价格调整
    const savePriceAdjustment = async () => {
      try {
        await priceFormRef.value.validate()
        
        const dish = pricingData.value.find(d => d.id === currentDish.value.id)
        if (dish) {
          const oldPrice = dish.currentPrice
          dish.currentPrice = priceForm.newPrice
          dish.priceChangePercent = ((priceForm.newPrice - dish.originalPrice) / dish.originalPrice * 100).toFixed(1)
          dish.priceChangeAmount = (priceForm.newPrice - dish.originalPrice).toFixed(2)
          dish.profitMargin = (((priceForm.newPrice - dish.cost) / priceForm.newPrice) * 100).toFixed(1)
          dish.lastAdjustment = new Date().toLocaleString()
          
          // 添加价格历史记录
          dish.priceHistory.push({
            date: new Date().toISOString().split('T')[0],
            price: priceForm.newPrice,
            reason: getReasionText(priceForm.reason)
          })

          pricingStats.monthlyChanges++
          message.success(`${dish.dishName} 价格调整成功`)
        }
        
        closePriceModal()
      } catch (error) {
        message.error('请检查表单信息')
      }
    }

    // 获取调价原因文本
    const getReasionText = (reason) => {
      const texts = {
        cost_increase: '成本上涨',
        cost_decrease: '成本下降',
        market_competition: '市场竞争',
        seasonal_adjustment: '季节调整',
        promotion_activity: '促销活动',
        profit_optimization: '利润优化',
        other: '其他原因'
      }
      return texts[reason] || reason
    }

    // 批量调价
    const batchPriceUpdate = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要调价的菜品')
        return
      }
      batchPriceModalVisible.value = true
    }

    // 执行批量调价
    const executeBatchPriceUpdate = () => {
      const selectedDishes = pricingData.value.filter(dish => 
        selectedRowKeys.value.includes(dish.id)
      )

      selectedDishes.forEach(dish => {
        let newPrice = dish.currentPrice

        switch (batchPriceForm.method) {
          case 'percentage':
            newPrice = dish.currentPrice * (1 + batchPriceForm.percentage / 100)
            break
          case 'fixed':
            newPrice = dish.currentPrice + batchPriceForm.amount
            break
          case 'set':
            newPrice = batchPriceForm.newPrice
            break
        }

        dish.currentPrice = parseFloat(newPrice.toFixed(2))
        dish.priceChangePercent = ((dish.currentPrice - dish.originalPrice) / dish.originalPrice * 100).toFixed(1)
        dish.priceChangeAmount = (dish.currentPrice - dish.originalPrice).toFixed(2)
        dish.profitMargin = (((dish.currentPrice - dish.cost) / dish.currentPrice) * 100).toFixed(1)
        dish.lastAdjustment = new Date().toLocaleString()
      })

      pricingStats.monthlyChanges += selectedDishes.length
      batchPriceModalVisible.value = false
      selectedRowKeys.value = []
      message.success(`批量调价成功，影响 ${selectedDishes.length} 个菜品`)
    }

    // 定价策略
    const priceStrategy = () => {
      strategyModalVisible.value = true
    }

    // 保存策略
    const saveStrategy = () => {
      strategyModalVisible.value = false
      message.success('定价策略保存成功')
    }

    // 价格历史
    const priceHistory = () => {
      message.info('价格历史记录功能')
    }

    // 导出价格表
    const exportPricing = () => {
      message.success('价格表导出成功')
    }

    // 刷新价格
    const refreshPricing = () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        message.success('价格数据刷新成功')
      }, 1000)
    }

    // 显示价格分析
    const showPriceAnalysis = () => {
      analysisModalVisible.value = true
      nextTick(() => {
        initPriceCharts()
      })
    }

    // 初始化价格图表
    const initPriceCharts = () => {
      // 价格趋势图
      const trendDom = document.getElementById('price-trend-chart')
      if (trendDom) {
        const trendChart = echarts.init(trendDom)
        const trendOption = {
          xAxis: {
            type: 'category',
            data: pricingData.value.map(item => item.dishName)
          },
          yAxis: {
            type: 'value',
            name: '价格(元)'
          },
          series: [{
            data: pricingData.value.map(item => item.currentPrice),
            type: 'line',
            smooth: true
          }]
        }
        trendChart.setOption(trendOption)
      }

      // 价格分布图
      const distributionDom = document.getElementById('price-distribution-chart')
      if (distributionDom) {
        const distributionChart = echarts.init(distributionDom)
        const distributionOption = {
          series: [{
            type: 'pie',
            radius: '50%',
            data: [
              { value: pricingData.value.filter(d => d.currentPrice <= 15).length, name: '低价菜品(≤15元)' },
              { value: pricingData.value.filter(d => d.currentPrice > 15 && d.currentPrice <= 30).length, name: '中价菜品(16-30元)' },
              { value: pricingData.value.filter(d => d.currentPrice > 30).length, name: '高价菜品(>30元)' }
            ]
          }]
        }
        distributionChart.setOption(distributionOption)
      }
    }

    // 处理更多操作
    const handleMoreAction = ({ key }) => {
      const [action, id] = key.split('-')
      const dish = pricingData.value.find(d => d.id === parseInt(id))
      
      switch (action) {
        case 'history':
          message.info(`查看价格历史: ${dish.dishName}`)
          break
        case 'analysis':
          message.info(`价格分析: ${dish.dishName}`)
          break
        case 'copy':
          message.info(`复制定价: ${dish.dishName}`)
          break
        case 'reset':
          dish.currentPrice = dish.originalPrice
          dish.priceChangePercent = 0
          dish.priceChangeAmount = 0
          dish.lastAdjustment = new Date().toLocaleString()
          message.success(`价格重置成功: ${dish.dishName}`)
          break
      }
    }

    onMounted(() => {
      // 初始化数据
    })

    return {
      loading,
      priceModalVisible,
      batchPriceModalVisible,
      analysisModalVisible,
      strategyModalVisible,
      selectedRowKeys,
      priceFormRef,
      currentDish,
      searchForm,
      pricingStats,
      priceForm,
      batchPriceForm,
      strategyForm,
      priceChangeInfo,
      priceFormRules,
      pricingData,
      pricingColumns,
      pagination,
      rowSelection,
      filteredPricing,
      getCategoryColor,
      getCategoryText,
      getPopularityColor,
      getStrategyColor,
      getStrategyText,
      onSearch,
      resetSearch,
      adjustPrice,
      calculatePriceChange,
      closePriceModal,
      savePriceAdjustment,
      batchPriceUpdate,
      executeBatchPriceUpdate,
      priceStrategy,
      saveStrategy,
      priceHistory,
      exportPricing,
      refreshPricing,
      showPriceAnalysis,
      handleMoreAction
    }
  }
}
</script>

<style scoped>
.stat-card {
  transition: all 0.3s;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.dish-info {
  display: flex;
  align-items: center;
}

.price-info {
  text-align: center;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
  margin-top: 4px;
}

.price-change {
  text-align: center;
}

.cost-info {
  font-size: 12px;
  line-height: 1.5;
}

.sales-data {
  font-size: 12px;
  line-height: 1.5;
}
</style>