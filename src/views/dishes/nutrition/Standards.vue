<template>
  <div class="dishes-nutrition-standards">
    <div class="page-title">营养标准设置</div>
    <placeholder-page 
      title="营养标准设置"
      description="营养标准设置功能正在开发中"
      icon="SettingOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesNutritionStandards',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '营养标准制定', description: '制定不同人群的营养摄入标准' },
      { title: '营养阈值设置', description: '设置营养成分的最低和最高阈值' },
      { title: '营养评级规则', description: '制定菜品营养评级规则和标准' },
      { title: '特殊需求配置', description: '特殊人群营养需求配置管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-nutrition-standards {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
