<template>
  <div class="dishes-nutrition-input">
    <div class="page-title">营养信息录入</div>
    <placeholder-page 
      title="营养信息录入"
      description="营养信息录入功能正在开发中"
      icon="EditOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DishesNutritionInput',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '营养成分录入', description: '详细的营养成分信息录入和编辑' },
      { title: '营养计算器', description: '自动计算营养成分和热量' },
      { title: '营养数据库', description: '丰富的食材营养数据库支持' },
      { title: '批量导入', description: '支持Excel批量导入营养信息' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dishes-nutrition-input {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
