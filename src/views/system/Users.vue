<template>
  <div class="system-users">
    <div class="page-title">用户管理</div>
    <placeholder-page 
      title="用户管理"
      description="用户管理功能正在开发中"
      icon="UserOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'SystemUsers',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '用户信息管理', description: '系统用户基础信息管理' },
      { title: '用户权限分配', description: '用户角色和权限分配管理' },
      { title: '用户状态管理', description: '用户账号状态启用/禁用管理' },
      { title: '用户操作日志', description: '用户操作行为日志记录' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.system-users {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
