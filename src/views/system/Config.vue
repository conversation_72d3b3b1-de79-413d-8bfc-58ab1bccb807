<template>
  <div class="system-config">
    <div class="page-title">系统配置</div>
    <placeholder-page 
      title="系统配置"
      description="系统配置功能正在开发中"
      icon="SettingOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'SystemConfig',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '系统参数配置', description: '系统运行参数配置管理' },
      { title: '业务规则配置', description: '业务流程规则配置' },
      { title: '接口配置', description: '第三方接口配置管理' },
      { title: '配置备份恢复', description: '系统配置备份和恢复' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.system-config {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
