<template>
  <div class="system-logs">
    <div class="page-title">系统日志</div>
    <placeholder-page 
      title="系统日志"
      description="系统日志功能正在开发中"
      icon="FileTextOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'SystemLogs',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '操作日志查询', description: '用户操作日志查询和分析' },
      { title: '系统日志监控', description: '系统运行日志实时监控' },
      { title: '异常日志告警', description: '系统异常日志自动告警' },
      { title: '日志数据导出', description: '日志数据导出和备份' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.system-logs {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
