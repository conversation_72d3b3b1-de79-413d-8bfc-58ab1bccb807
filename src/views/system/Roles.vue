<template>
  <div class="system-roles">
    <div class="page-title">角色管理</div>
    <placeholder-page 
      title="角色管理"
      description="角色管理功能正在开发中"
      icon="TeamOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'SystemRoles',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '角色定义管理', description: '系统角色定义和描述管理' },
      { title: '权限配置', description: '角色权限配置和分配' },
      { title: '角色继承', description: '角色权限继承关系管理' },
      { title: '权限审计', description: '角色权限变更审计记录' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.system-roles {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
