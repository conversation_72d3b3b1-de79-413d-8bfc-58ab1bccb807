<template>
  <div class="system-data">
    <div class="page-title">数据管理</div>
    <placeholder-page 
      title="数据管理"
      description="数据管理功能正在开发中"
      icon="DatabaseOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'SystemData',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '数据备份', description: '系统数据定期备份管理' },
      { title: '数据恢复', description: '数据恢复和还原功能' },
      { title: '数据清理', description: '过期数据自动清理' },
      { title: '数据统计', description: '系统数据使用情况统计' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.system-data {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
