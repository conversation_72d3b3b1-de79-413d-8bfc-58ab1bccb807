<template>
  <div class="dashboard-pending">
    <div class="page-title">待处理事项</div>
    <placeholder-page 
      title="待处理事项"
      description="待处理事项功能正在开发中"
      icon="ExclamationCircleOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'DashboardPending',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '待审核菜品', description: '需要审核的新增菜品和修改申请' },
      { title: '异常订单处理', description: '需要人工处理的异常订单' },
      { title: '库存预警', description: '低库存和过期食材预警提醒' },
      { title: '系统通知', description: '重要的系统通知和提醒事项' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.dashboard-pending {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
