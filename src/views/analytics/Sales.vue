<template>
  <div class="analytics-sales">
    <div class="page-title">销售分析</div>
    <placeholder-page 
      title="销售分析"
      description="销售分析功能正在开发中"
      icon="BarChartOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'AnalyticsSales',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '销售数据统计', description: '全面的销售数据统计分析' },
      { title: '销售趋势分析', description: '销售趋势变化和预测分析' },
      { title: '热销商品分析', description: '热销商品排行和分析' },
      { title: '销售报表生成', description: '自动生成各类销售报表' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.analytics-sales {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
