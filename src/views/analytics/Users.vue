<template>
  <div class="analytics-users">
    <div class="page-title">用户分析</div>
    <placeholder-page 
      title="用户分析"
      description="用户分析功能正在开发中"
      icon="UserOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'AnalyticsUsers',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '用户行为分析', description: '用户消费行为和偏好分析' },
      { title: '用户画像构建', description: '基于数据的用户画像构建' },
      { title: '用户留存分析', description: '用户留存率和活跃度分析' },
      { title: '用户价值分析', description: '用户生命周期价值分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.analytics-users {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
