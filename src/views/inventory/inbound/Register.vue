<template>
  <div class="inventory-inbound-register">
    <div class="page-title">入库登记</div>
    <placeholder-page 
      title="入库登记"
      description="入库登记功能正在开发中"
      icon="EditOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryInboundRegister',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '入库单创建', description: '创建和编辑入库单据信息' },
      { title: '批量入库', description: '支持批量食材入库登记' },
      { title: '条码扫描', description: '支持条码扫描快速入库' },
      { title: '入库凭证', description: '入库凭证上传和管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-inbound-register {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
