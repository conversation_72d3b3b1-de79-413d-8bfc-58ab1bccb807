<template>
  <div class="inventory-inbound-audit">
    <div class="page-title">入库审核</div>
    <placeholder-page 
      title="入库审核"
      description="入库审核功能正在开发中"
      icon="AuditOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryInboundAudit',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '入库审核流程', description: '多级入库审核流程管理' },
      { title: '审核标准设置', description: '入库审核标准和规则设置' },
      { title: '批量审核', description: '支持批量入库单据审核' },
      { title: '审核记录', description: '完整的审核记录和意见管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-inbound-audit {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
