<template>
  <div class="inventory-inbound-quality">
    <div class="page-title">质检记录</div>
    <placeholder-page 
      title="质检记录"
      description="质检记录功能正在开发中"
      icon="SafetyCertificateOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryInboundQuality',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '质检标准管理', description: '食材质检标准和检测项目管理' },
      { title: '质检记录录入', description: '详细的质检结果记录和评级' },
      { title: '不合格处理', description: '不合格食材的处理流程和记录' },
      { title: '质检报告', description: '质检报告生成和统计分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-inbound-quality {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
