<template>
  <div class="inventory-monitor">
    <div class="page-title">库存监控</div>
    <placeholder-page 
      title="库存监控"
      description="库存监控功能正在开发中"
      icon="MonitorOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryMonitor',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '实时库存监控', description: '实时监控所有食材库存状态' },
      { title: '库存预警', description: '低库存和过期食材自动预警' },
      { title: '库存报表', description: '库存变化趋势和统计报表' },
      { title: '库存优化建议', description: '基于数据分析的库存优化建议' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-monitor {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
