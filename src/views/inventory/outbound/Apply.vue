<template>
  <div class="inventory-outbound-apply">
    <div class="page-title">出库申请</div>
    <placeholder-page 
      title="出库申请"
      description="出库申请功能正在开发中"
      icon="FormOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryOutboundApply',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '出库申请单', description: '创建和提交出库申请单据' },
      { title: '用料计划', description: '基于菜品制作的用料计划申请' },
      { title: '紧急出库', description: '紧急情况下的快速出库申请' },
      { title: '申请状态跟踪', description: '出库申请状态实时跟踪' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-outbound-apply {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
