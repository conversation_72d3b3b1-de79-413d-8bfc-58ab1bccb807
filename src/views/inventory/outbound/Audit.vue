<template>
  <div class="inventory-outbound-audit">
    <div class="page-title">出库审核</div>
    <placeholder-page 
      title="出库审核"
      description="出库审核功能正在开发中"
      icon="AuditOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryOutboundAudit',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '出库审核流程', description: '多级出库审核流程和权限管理' },
      { title: '库存检查', description: '出库前的库存数量和状态检查' },
      { title: '批量审核', description: '支持批量出库申请审核' },
      { title: '审核记录', description: '详细的审核记录和决策依据' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-outbound-audit {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
