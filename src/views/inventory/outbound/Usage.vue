<template>
  <div class="inventory-outbound-usage">
    <div class="page-title">用料统计</div>
    <placeholder-page 
      title="用料统计"
      description="用料统计功能正在开发中"
      icon="BarChartOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryOutboundUsage',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '用料统计报表', description: '各类食材的使用量统计报表' },
      { title: '成本分析', description: '食材使用成本分析和趋势' },
      { title: '效率分析', description: '食材使用效率和浪费分析' },
      { title: '预测建议', description: '基于历史数据的用料预测建议' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-outbound-usage {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
