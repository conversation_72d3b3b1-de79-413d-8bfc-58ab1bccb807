<template>
  <div class="inventory-ingredients-archive">
    <div class="page-title">食材档案</div>
    <placeholder-page 
      title="食材档案"
      description="食材档案管理功能正在开发中"
      icon="FileTextOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryIngredientsArchive',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '食材基础信息', description: '食材名称、规格、产地等基础信息管理' },
      { title: '供应商信息', description: '食材供应商和采购渠道信息' },
      { title: '营养成分', description: '食材营养成分和热量信息' },
      { title: '存储要求', description: '食材存储条件和保质期要求' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-ingredients-archive {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
