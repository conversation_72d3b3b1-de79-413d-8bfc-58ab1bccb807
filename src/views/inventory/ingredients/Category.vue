<template>
  <div class="inventory-ingredients-category">
    <div class="page-title">食材分类</div>
    <placeholder-page 
      title="食材分类"
      description="食材分类管理功能正在开发中"
      icon="FolderOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryIngredientsCategory',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '分类层级管理', description: '食材分类的层级结构管理' },
      { title: '分类属性设置', description: '不同分类的特殊属性和要求设置' },
      { title: '分类标签管理', description: '食材分类标签和标识管理' },
      { title: '分类统计分析', description: '各分类食材的使用统计分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-ingredients-category {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
