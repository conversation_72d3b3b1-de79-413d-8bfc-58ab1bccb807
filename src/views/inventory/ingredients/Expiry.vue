<template>
  <div class="inventory-ingredients-expiry">
    <div class="page-title">保质期管理</div>
    <placeholder-page 
      title="保质期管理"
      description="保质期管理功能正在开发中"
      icon="ClockCircleOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryIngredientsExpiry',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '保质期监控', description: '实时监控食材保质期状态' },
      { title: '过期预警', description: '临近过期和已过期食材预警' },
      { title: '批次管理', description: '不同批次食材的保质期跟踪' },
      { title: '处理记录', description: '过期食材的处理记录和统计' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-ingredients-expiry {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
