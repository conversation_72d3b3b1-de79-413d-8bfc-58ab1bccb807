<template>
  <div class="inventory-outbound">
    <div class="page-title">出库管理</div>
    <placeholder-page 
      title="出库管理"
      description="出库管理功能正在开发中"
      icon="ExportOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryOutbound',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '出库单管理', description: '创建和管理食材出库单据' },
      { title: '批量出库', description: '支持批量食材出库操作' },
      { title: '出库审核', description: '出库申请审核和确认流程' },
      { title: '出库统计', description: '出库数据统计和分析报表' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-outbound {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
