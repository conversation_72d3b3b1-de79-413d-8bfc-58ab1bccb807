<template>
  <div class="inventory-ingredients">
    <div class="page-title">食材管理</div>
    <placeholder-page 
      title="食材管理"
      description="食材库存管理功能正在开发中"
      icon="AppleOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryIngredients',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '食材信息管理', description: '完整的食材基础信息和规格管理' },
      { title: '库存实时监控', description: '实时监控食材库存数量和状态' },
      { title: '保质期管理', description: '食材保质期跟踪和过期提醒' },
      { title: '库存预警', description: '低库存自动预警，避免缺货情况' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-ingredients {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
