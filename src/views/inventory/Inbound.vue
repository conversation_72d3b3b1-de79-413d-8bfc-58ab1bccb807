<template>
  <div class="inventory-inbound">
    <div class="page-title">入库管理</div>
    <placeholder-page 
      title="入库管理"
      description="入库管理功能正在开发中"
      icon="ImportOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryInbound',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '入库单管理', description: '创建和管理食材入库单据' },
      { title: '批量入库', description: '支持批量食材入库操作' },
      { title: '质量检验', description: '入库食材质量检验和记录' },
      { title: '入库统计', description: '入库数据统计和分析报表' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-inbound {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
