<template>
  <div class="inventory-monitor-loss">
    <div class="page-title">损耗统计</div>
    <placeholder-page 
      title="损耗统计"
      description="损耗统计功能正在开发中"
      icon="FallOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryMonitorLoss',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '损耗记录', description: '详细的食材损耗记录和原因分析' },
      { title: '损耗率统计', description: '各类食材的损耗率统计和趋势' },
      { title: '成本影响分析', description: '损耗对成本的影响分析' },
      { title: '改进建议', description: '基于损耗分析的改进建议' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-monitor-loss {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
