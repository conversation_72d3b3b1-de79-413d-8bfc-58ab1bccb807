<template>
  <div class="inventory-monitor-query">
    <div class="page-title">库存查询</div>
    <placeholder-page 
      title="库存查询"
      description="库存查询功能正在开发中"
      icon="SearchOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryMonitorQuery',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '实时库存查询', description: '实时查询各类食材的库存数量' },
      { title: '多维度筛选', description: '按分类、供应商、时间等维度筛选' },
      { title: '库存明细', description: '详细的库存明细和批次信息' },
      { title: '导出功能', description: '库存数据导出和报表生成' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-monitor-query {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
