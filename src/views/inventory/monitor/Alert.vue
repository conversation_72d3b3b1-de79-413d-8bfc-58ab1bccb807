<template>
  <div class="inventory-monitor-alert">
    <div class="page-title">库存预警</div>
    <placeholder-page 
      title="库存预警"
      description="库存预警功能正在开发中"
      icon="AlertOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'InventoryMonitorAlert',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '低库存预警', description: '库存数量低于安全线时的预警提醒' },
      { title: '过期预警', description: '食材临近过期和已过期的预警' },
      { title: '预警规则设置', description: '灵活的预警规则和阈值设置' },
      { title: '预警通知', description: '多渠道预警通知和处理跟踪' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.inventory-monitor-alert {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
