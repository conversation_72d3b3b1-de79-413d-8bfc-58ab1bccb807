<template>
  <div class="nutrition-safety">
    <div class="page-title">食品安全</div>
    <placeholder-page 
      title="食品安全"
      description="食品安全功能正在开发中"
      icon="SafetyOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'NutritionSafety',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '食品安全监控', description: '食品安全指标实时监控' },
      { title: '安全检测记录', description: '食品安全检测记录管理' },
      { title: '安全预警系统', description: '食品安全风险预警和处理' },
      { title: '安全报告生成', description: '食品安全检查报告生成' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.nutrition-safety {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
