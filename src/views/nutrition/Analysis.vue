<template>
  <div class="nutrition-analysis">
    <div class="page-title">营养分析</div>
    <placeholder-page 
      title="营养分析"
      description="营养分析功能正在开发中"
      icon="PieChartOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'NutritionAnalysis',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '营养摄入分析', description: '用户营养摄入情况分析' },
      { title: '营养缺失提醒', description: '营养缺失自动检测和提醒' },
      { title: '营养趋势分析', description: '营养摄入趋势变化分析' },
      { title: '营养报告导出', description: '营养分析报告导出功能' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.nutrition-analysis {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
