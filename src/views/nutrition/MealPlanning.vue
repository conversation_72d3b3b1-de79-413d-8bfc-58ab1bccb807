<template>
  <div class="nutrition-meal-planning">
    <div class="page-title">营养配餐</div>
    <placeholder-page 
      title="营养配餐"
      description="营养配餐功能正在开发中"
      icon="AppleOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'NutritionMealPlanning',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '智能配餐推荐', description: '基于营养需求的智能配餐推荐' },
      { title: '营养成分计算', description: '自动计算配餐的营养成分' },
      { title: '个性化配餐', description: '根据个人健康状况定制配餐' },
      { title: '配餐方案管理', description: '配餐方案的创建、编辑和管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.nutrition-meal-planning {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
