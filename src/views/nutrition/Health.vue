<template>
  <div class="nutrition-health">
    <div class="page-title">健康管理</div>
    <placeholder-page 
      title="健康管理"
      description="健康管理功能正在开发中"
      icon="UserOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'NutritionHealth',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '健康档案管理', description: '用户健康信息档案管理' },
      { title: '健康指标监控', description: 'BMI、血压等健康指标监控' },
      { title: '饮食建议', description: '基于健康状况的饮食建议' },
      { title: '健康报告生成', description: '定期生成个人健康报告' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.nutrition-health {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
