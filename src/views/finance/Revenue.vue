<template>
  <div class="finance-revenue">
    <div class="page-title">收入管理</div>
    <placeholder-page 
      title="收入管理"
      description="收入管理功能正在开发中"
      icon="RiseOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'FinanceRevenue',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '收入统计分析', description: '日、周、月、年收入统计分析' },
      { title: '收入来源分析', description: '不同菜品和服务的收入贡献分析' },
      { title: '收入趋势预测', description: '基于历史数据的收入趋势预测' },
      { title: '收入报表生成', description: '自动生成各类收入分析报表' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.finance-revenue {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
