<template>
  <div class="finance-cost">
    <div class="page-title">成本管理</div>
    <placeholder-page 
      title="成本管理"
      description="成本管理功能正在开发中"
      icon="FallOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'FinanceCost',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '成本核算管理', description: '食材、人工、运营等成本核算' },
      { title: '成本控制分析', description: '成本变化趋势和控制措施分析' },
      { title: '成本预算管理', description: '成本预算制定和执行监控' },
      { title: '成本优化建议', description: '基于数据分析的成本优化建议' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.finance-cost {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
