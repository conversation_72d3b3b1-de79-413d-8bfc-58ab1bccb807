<template>
  <div class="finance-reports">
    <div class="page-title">财务报表</div>
    <placeholder-page 
      title="财务报表"
      description="财务报表功能正在开发中"
      icon="FileExcelOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'FinanceReports',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '财务报表生成', description: '自动生成各类财务报表' },
      { title: '报表模板管理', description: '自定义报表模板和格式' },
      { title: '报表数据导出', description: '支持多种格式的报表导出' },
      { title: '报表分析工具', description: '内置报表数据分析工具' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.finance-reports {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
