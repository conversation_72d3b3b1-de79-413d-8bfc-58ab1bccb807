<template>
  <div class="finance-profit">
    <div class="page-title">利润分析</div>
    <placeholder-page 
      title="利润分析"
      description="利润分析功能正在开发中"
      icon="LineChartOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'FinanceProfit',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '利润率分析', description: '整体和单品利润率分析' },
      { title: '利润趋势分析', description: '利润变化趋势和影响因素分析' },
      { title: '盈亏平衡分析', description: '盈亏平衡点分析和预测' },
      { title: '利润优化建议', description: '提升利润的策略建议' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.finance-profit {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
