<template>
  <div class="procurement-suppliers">
    <div class="page-title">供应商管理</div>
    <placeholder-page 
      title="供应商管理"
      description="供应商管理功能正在开发中"
      icon="BankOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'ProcurementSuppliers',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '供应商信息管理', description: '完整的供应商基础信息管理' },
      { title: '供应商评估', description: '供应商资质和服务质量评估' },
      { title: '合作协议管理', description: '供应商合作协议和合同管理' },
      { title: '供应商绩效分析', description: '供应商绩效数据统计和分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.procurement-suppliers {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
