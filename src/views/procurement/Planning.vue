<template>
  <div class="procurement-planning">
    <div class="page-title">采购计划</div>
    <placeholder-page 
      title="采购计划"
      description="采购计划功能正在开发中"
      icon="ScheduleOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'ProcurementPlanning',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '采购计划制定', description: '根据需求制定采购计划' },
      { title: '智能需求预测', description: '基于历史数据预测采购需求' },
      { title: '采购计划审批', description: '采购计划审批流程管理' },
      { title: '计划执行跟踪', description: '采购计划执行情况跟踪' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.procurement-planning {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
