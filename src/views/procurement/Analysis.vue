<template>
  <div class="procurement-analysis">
    <div class="page-title">采购分析</div>
    <placeholder-page 
      title="采购分析"
      description="采购分析功能正在开发中"
      icon="BarChartOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'ProcurementAnalysis',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '采购成本分析', description: '采购成本趋势和结构分析' },
      { title: '供应商绩效分析', description: '供应商服务质量和效率分析' },
      { title: '采购效率分析', description: '采购流程效率和优化建议' },
      { title: '采购预算分析', description: '采购预算执行情况分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.procurement-analysis {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
