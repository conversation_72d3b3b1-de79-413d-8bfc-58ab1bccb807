<template>
  <div class="procurement-orders">
    <div class="page-title">采购订单</div>
    <placeholder-page 
      title="采购订单"
      description="采购订单功能正在开发中"
      icon="FileTextOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'ProcurementOrders',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '采购订单管理', description: '创建和管理采购订单' },
      { title: '订单状态跟踪', description: '实时跟踪采购订单状态' },
      { title: '供应商对接', description: '与供应商系统对接管理' },
      { title: '订单数据分析', description: '采购订单数据统计分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.procurement-orders {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
