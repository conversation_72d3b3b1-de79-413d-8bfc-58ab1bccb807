<template>
  <div class="orders-history">
    <div class="page-title">历史订单</div>
    
    <!-- 筛选区域 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="时间范围">
          <a-range-picker 
            v-model:value="filterForm.dateRange" 
            @change="handleFilter"
            :presets="datePresets"
          />
        </a-form-item>
        
        <a-form-item label="订单状态">
          <a-select v-model:value="filterForm.status" style="width: 120px" @change="handleFilter">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="cancelled">已取消</a-select-option>
            <a-select-option value="refunded">已退款</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="订单号">
          <a-input 
            v-model:value="filterForm.orderNo" 
            placeholder="请输入订单号"
            style="width: 200px"
            @pressEnter="handleFilter"
          />
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleFilter">
            <search-outlined />
            搜索
          </a-button>
          <a-button @click="resetFilter" style="margin-left: 8px">
            重置
          </a-button>
          <a-button @click="exportData" style="margin-left: 8px">
            <download-outlined />
            导出
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="总订单数"
            :value="historyStats.totalOrders"
            suffix="单"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="已完成订单"
            :value="historyStats.completedOrders"
            suffix="单"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="已取消订单"
            :value="historyStats.cancelledOrders"
            suffix="单"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="总营业额"
            :value="historyStats.totalRevenue"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#3f8600' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 订单列表 -->
    <a-table
      :columns="columns"
      :data-source="orderList"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'orderNo'">
          <a-button type="link" @click="viewDetail(record)">
            {{ record.orderNo }}
          </a-button>
        </template>
        
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="viewDetail(record)">
              <eye-outlined />
              查看详情
            </a-button>
            <a-button 
              v-if="record.status === 'completed'" 
              type="link" 
              size="small" 
              @click="reorder(record)"
            >
              <redo-outlined />
              再次下单
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 订单详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="订单详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentOrder">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="订单号">{{ currentOrder.orderNo }}</a-descriptions-item>
          <a-descriptions-item label="下单时间">{{ currentOrder.createTime }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ currentOrder.customerName }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ currentOrder.phone }}</a-descriptions-item>
          <a-descriptions-item label="订单状态">
            <a-tag :color="getStatusColor(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="支付状态">
            <a-tag :color="getPaymentStatusColor(currentOrder.paymentStatus)">
              {{ getPaymentStatusText(currentOrder.paymentStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="订单金额">¥{{ currentOrder.totalAmount }}</a-descriptions-item>
          <a-descriptions-item label="支付方式">{{ currentOrder.paymentMethod }}</a-descriptions-item>
          <a-descriptions-item label="完成时间" v-if="currentOrder.completeTime">{{ currentOrder.completeTime }}</a-descriptions-item>
          <a-descriptions-item label="备注信息" :span="2">{{ currentOrder.remark || '无' }}</a-descriptions-item>
        </a-descriptions>
        
        <div style="margin-top: 16px;">
          <h4>订单商品</h4>
          <a-table
            :columns="itemColumns"
            :data-source="currentOrder.items"
            :pagination="false"
            size="small"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  DownloadOutlined,
  EyeOutlined,
  RedoOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'OrdersHistory',
  components: {
    SearchOutlined,
    DownloadOutlined,
    EyeOutlined,
    RedoOutlined
  },
  setup() {
    const loading = ref(false)
    const detailModalVisible = ref(false)
    const currentOrder = ref(null)
    
    // 历史统计数据
    const historyStats = ref({
      totalOrders: 2580,
      completedOrders: 2350,
      cancelledOrders: 230,
      totalRevenue: 125680.50
    })
    
    // 筛选表单
    const filterForm = reactive({
      dateRange: [dayjs().subtract(30, 'day'), dayjs()],
      status: '',
      orderNo: ''
    })

    // 日期预设
    const datePresets = [
      { label: '今天', value: [dayjs(), dayjs()] },
      { label: '昨天', value: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] },
      { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
      { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
      { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
      { label: '上月', value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] }
    ]

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    })

    // 表格列配置
    const columns = [
      {
        title: '订单号',
        key: 'orderNo',
        width: 150
      },
      {
        title: '客户姓名',
        dataIndex: 'customerName',
        key: 'customerName',
        width: 100
      },
      {
        title: '订单金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        width: 100,
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '下单时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 150
      },
      {
        title: '完成时间',
        dataIndex: 'completeTime',
        key: 'completeTime',
        width: 150
      },
      {
        title: '订单状态',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right'
      }
    ]

    // 订单商品列配置
    const itemColumns = [
      {
        title: '商品名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '单价',
        dataIndex: 'price',
        key: 'price',
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity'
      },
      {
        title: '小计',
        dataIndex: 'subtotal',
        key: 'subtotal',
        customRender: ({ text }) => `¥${text}`
      }
    ]

    const orderList = ref([])

    // 生成模拟数据
    const generateMockData = () => {
      const mockData = []
      const statuses = ['completed', 'cancelled', 'refunded']
      const paymentStatuses = ['paid', 'refunded']
      const names = ['张三', '李四', '王五', '赵六', '钱七']
      const dishes = ['宫保鸡丁', '麻婆豆腐', '红烧肉', '西红柿鸡蛋', '青椒土豆丝']
      
      for (let i = 1; i <= 100; i++) {
        const items = []
        const itemCount = Math.floor(Math.random() * 3) + 1
        let totalAmount = 0
        
        for (let j = 0; j < itemCount; j++) {
          const price = (Math.random() * 20 + 10).toFixed(2)
          const quantity = Math.floor(Math.random() * 3) + 1
          const subtotal = (price * quantity).toFixed(2)
          totalAmount += parseFloat(subtotal)
          
          items.push({
            name: dishes[Math.floor(Math.random() * dishes.length)],
            price: price,
            quantity: quantity,
            subtotal: subtotal
          })
        }
        
        const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
        const completeTime = new Date(createTime.getTime() + Math.random() * 2 * 60 * 60 * 1000)
        
        mockData.push({
          id: i,
          orderNo: `ORD${createTime.getFullYear()}${String(createTime.getMonth() + 1).padStart(2, '0')}${String(createTime.getDate()).padStart(2, '0')}${String(i).padStart(4, '0')}`,
          customerName: names[Math.floor(Math.random() * names.length)],
          phone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
          totalAmount: totalAmount.toFixed(2),
          createTime: createTime.toLocaleString(),
          completeTime: completeTime.toLocaleString(),
          status: statuses[Math.floor(Math.random() * statuses.length)],
          paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
          paymentMethod: ['微信支付', '支付宝', '现金'][Math.floor(Math.random() * 3)],
          remark: Math.random() > 0.5 ? '少辣' : '',
          items: items
        })
      }
      return mockData
    }

    // 状态相关方法
    const getStatusColor = (status) => {
      const colors = {
        completed: 'green',
        cancelled: 'red',
        refunded: 'orange'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        completed: '已完成',
        cancelled: '已取消',
        refunded: '已退款'
      }
      return texts[status] || '未知'
    }

    const getPaymentStatusColor = (status) => {
      const colors = {
        paid: 'green',
        refunded: 'orange'
      }
      return colors[status] || 'default'
    }

    const getPaymentStatusText = (status) => {
      const texts = {
        paid: '已支付',
        refunded: '已退款'
      }
      return texts[status] || '未知'
    }

    // 数据加载
    const loadData = async () => {
      loading.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const mockData = generateMockData()
        let filteredData = mockData

        // 应用筛选条件
        if (filterForm.status) {
          filteredData = filteredData.filter(item => item.status === filterForm.status)
        }
        if (filterForm.orderNo) {
          filteredData = filteredData.filter(item => 
            item.orderNo.toLowerCase().includes(filterForm.orderNo.toLowerCase())
          )
        }
        if (filterForm.dateRange && filterForm.dateRange.length === 2) {
          const startDate = filterForm.dateRange[0].startOf('day')
          const endDate = filterForm.dateRange[1].endOf('day')
          filteredData = filteredData.filter(item => {
            const itemDate = dayjs(item.createTime)
            return itemDate.isAfter(startDate) && itemDate.isBefore(endDate)
          })
        }

        pagination.total = filteredData.length
        const start = (pagination.current - 1) * pagination.pageSize
        const end = start + pagination.pageSize
        orderList.value = filteredData.slice(start, end)
      } catch (error) {
        message.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    // 操作方法
    const handleFilter = () => {
      pagination.current = 1
      loadData()
    }

    const resetFilter = () => {
      Object.assign(filterForm, {
        dateRange: [dayjs().subtract(30, 'day'), dayjs()],
        status: '',
        orderNo: ''
      })
      handleFilter()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadData()
    }

    const viewDetail = (record) => {
      currentOrder.value = record
      detailModalVisible.value = true
    }

    const reorder = (record) => {
      message.info('再次下单功能开发中...')
    }

    const exportData = () => {
      message.info('导出功能开发中...')
    }

    onMounted(() => {
      loadData()
    })

    return {
      loading,
      detailModalVisible,
      currentOrder,
      historyStats,
      filterForm,
      datePresets,
      pagination,
      columns,
      itemColumns,
      orderList,
      getStatusColor,
      getStatusText,
      getPaymentStatusColor,
      getPaymentStatusText,
      handleFilter,
      resetFilter,
      handleTableChange,
      viewDetail,
      reorder,
      exportData
    }
  }
}
</script>

<style scoped>
.orders-history {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

.filter-card {
  margin-bottom: 24px;
}

.stat-card {
  margin-bottom: 24px;
}
</style>
