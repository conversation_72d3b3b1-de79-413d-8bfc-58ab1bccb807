<template>
  <div class="orders-queue-numbers">
    <div class="page-title">取餐号码管理</div>
    <placeholder-page 
      title="取餐号码管理"
      description="取餐号码管理功能正在开发中"
      icon="NumberOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersQueueNumbers',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '号码生成规则', description: '取餐号码的生成规则和格式设置' },
      { title: '号码分配管理', description: '不同窗口和时段的号码分配' },
      { title: '号码状态跟踪', description: '已叫号、未取餐等状态跟踪' },
      { title: '号码重置管理', description: '每日号码重置和历史记录管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-queue-numbers {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
