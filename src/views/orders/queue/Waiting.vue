<template>
  <div class="orders-queue-waiting">
    <div class="page-title">等待时间预估</div>
    <placeholder-page 
      title="等待时间预估"
      description="等待时间预估功能正在开发中"
      icon="ClockCircleOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersQueueWaiting',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '智能时间预估', description: '基于历史数据的智能等待时间预估' },
      { title: '实时时间更新', description: '根据当前情况实时更新等待时间' },
      { title: '预估准确度分析', description: '预估时间准确度统计和优化' },
      { title: '用户通知推送', description: '等待时间变化的用户通知推送' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-queue-waiting {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
