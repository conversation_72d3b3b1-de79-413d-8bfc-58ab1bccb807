<template>
  <div class="orders-queue-status">
    <div class="page-title">实时排队状况</div>
    <placeholder-page 
      title="实时排队状况"
      description="实时排队状况功能正在开发中"
      icon="EyeOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersQueueStatus',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '实时排队监控', description: '实时显示各窗口排队情况' },
      { title: '排队人数统计', description: '当前排队人数和预计等待时间' },
      { title: '窗口状态管理', description: '各个取餐窗口的开放状态管理' },
      { title: '排队预警', description: '排队人数过多时的预警提醒' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-queue-status {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
