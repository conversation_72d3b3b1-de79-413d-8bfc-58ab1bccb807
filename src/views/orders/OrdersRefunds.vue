<template>
  <div class="orders-refunds">
    <div class="page-title">退款处理</div>
    <placeholder-page 
      title="退款处理"
      description="订单退款处理功能正在开发中"
      icon="RollbackOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersRefunds',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '退款申请管理', description: '处理客户退款申请，支持多种退款原因' },
      { title: '退款审核流程', description: '多级审核机制，确保退款处理的准确性' },
      { title: '自动退款处理', description: '符合条件的退款申请可自动处理' },
      { title: '退款统计分析', description: '退款数据统计分析，优化服务质量' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-refunds {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
