<template>
  <div class="orders-payment">
    <div class="page-title">支付管理</div>
    <placeholder-page 
      title="支付管理"
      description="支付管理功能正在开发中"
      icon="CreditCardOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersPayment',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '多种支付方式', description: '支持微信、支付宝、银行卡等多种支付方式' },
      { title: '支付状态监控', description: '实时监控支付状态，及时处理异常' },
      { title: '支付数据统计', description: '支付方式偏好和成功率统计分析' },
      { title: '退款处理', description: '快速处理退款申请，提升客户体验' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-payment {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
