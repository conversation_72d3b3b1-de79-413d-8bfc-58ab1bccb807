<template>
  <div class="orders-tracking">
    <div class="page-title">订单状态跟踪</div>
    <placeholder-page 
      title="订单状态跟踪"
      description="订单实时状态跟踪功能正在开发中"
      icon="EyeOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersTracking',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '实时状态更新', description: '订单状态实时同步，及时了解订单进度' },
      { title: '状态流程图', description: '可视化展示订单处理流程和当前状态' },
      { title: '异常订单提醒', description: '自动识别异常订单并及时提醒处理' },
      { title: '批量状态管理', description: '支持批量更新订单状态，提高处理效率' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-tracking {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
