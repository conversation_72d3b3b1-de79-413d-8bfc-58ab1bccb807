<template>
  <div class="orders-delivery">
    <div class="page-title">配送管理</div>
    <placeholder-page 
      title="配送管理"
      description="配送管理功能正在开发中"
      icon="CarOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersDelivery',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '配送员管理', description: '配送员信息管理和工作安排' },
      { title: '配送路线优化', description: '智能规划配送路线，提高配送效率' },
      { title: '实时位置跟踪', description: '实时跟踪配送员位置和配送进度' },
      { title: '配送数据统计', description: '配送效率和客户满意度数据分析' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-delivery {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
