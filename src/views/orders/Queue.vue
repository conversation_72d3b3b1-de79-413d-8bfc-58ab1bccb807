<template>
  <div class="orders-queue">
    <div class="page-title">排队取餐管理</div>
    <placeholder-page 
      title="排队取餐管理"
      description="排队取餐管理功能正在开发中"
      icon="TeamOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersQueue',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '智能排队系统', description: '自动分配取餐号码，优化排队流程' },
      { title: '实时队列监控', description: '实时显示排队状态和预计等待时间' },
      { title: '语音叫号提醒', description: '自动语音叫号，提升取餐体验' },
      { title: '队列数据分析', description: '分析排队数据，优化服务效率' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-queue {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
