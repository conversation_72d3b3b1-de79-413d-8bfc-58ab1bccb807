<template>
  <div class="orders-delivery-routes">
    <div class="page-title">配送路线规划</div>
    <placeholder-page 
      title="配送路线规划"
      description="配送路线规划功能正在开发中"
      icon="EnvironmentOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersDeliveryRoutes',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '智能路线规划', description: '基于地理位置的智能配送路线规划' },
      { title: '路线优化算法', description: '最短路径和最优时间的路线优化' },
      { title: '实时路况考虑', description: '结合实时路况信息的路线调整' },
      { title: '配送区域管理', description: '配送区域划分和配送员分配' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-delivery-routes {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
