<template>
  <div class="orders-delivery-staff">
    <div class="page-title">配送员管理</div>
    <placeholder-page 
      title="配送员管理"
      description="配送员管理功能正在开发中"
      icon="UserOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersDeliveryStaff',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '配送员档案', description: '配送员基本信息和资质管理' },
      { title: '工作状态管理', description: '配送员在线状态和工作安排' },
      { title: '绩效考核', description: '配送员绩效统计和考核评价' },
      { title: '培训管理', description: '配送员培训记录和证书管理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-delivery-staff {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
