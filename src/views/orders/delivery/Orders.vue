<template>
  <div class="orders-delivery-orders">
    <div class="page-title">配送订单</div>
    <placeholder-page 
      title="配送订单"
      description="配送订单功能正在开发中"
      icon="FileTextOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersDeliveryOrders',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '配送订单管理', description: '配送订单的创建、分配和跟踪' },
      { title: '配送状态更新', description: '实时更新配送状态和位置信息' },
      { title: '配送时效监控', description: '配送时效监控和超时预警' },
      { title: '配送异常处理', description: '配送异常情况的处理和记录' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-delivery-orders {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
