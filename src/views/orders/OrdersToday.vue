<template>
  <div class="orders-today">
    <div class="page-title">今日订单</div>
    
    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <a-button type="primary" @click="refreshData">
        <reload-outlined />
        刷新数据
      </a-button>
      <a-button @click="batchProcess" :disabled="!hasSelected">
        <check-outlined />
        批量处理
      </a-button>
      <a-button @click="exportData">
        <download-outlined />
        导出数据
      </a-button>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="今日订单总数"
            :value="todayStats.totalOrders"
            suffix="单"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <shopping-cart-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="待处理订单"
            :value="todayStats.pendingOrders"
            suffix="单"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <clock-circle-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="已完成订单"
            :value="todayStats.completedOrders"
            suffix="单"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <check-circle-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card">
          <a-statistic
            title="今日营业额"
            :value="todayStats.totalRevenue"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <dollar-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 筛选区域 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="订单状态">
          <a-select v-model:value="filterForm.status" style="width: 120px" @change="handleFilter">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="pending">待处理</a-select-option>
            <a-select-option value="preparing">制作中</a-select-option>
            <a-select-option value="ready">待取餐</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="cancelled">已取消</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="支付状态">
          <a-select v-model:value="filterForm.paymentStatus" style="width: 120px" @change="handleFilter">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="unpaid">未支付</a-select-option>
            <a-select-option value="paid">已支付</a-select-option>
            <a-select-option value="refunded">已退款</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="订单号">
          <a-input 
            v-model:value="filterForm.orderNo" 
            placeholder="请输入订单号"
            style="width: 200px"
            @pressEnter="handleFilter"
          />
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleFilter">
            <search-outlined />
            搜索
          </a-button>
          <a-button @click="resetFilter" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 订单列表 -->
    <a-table
      :columns="columns"
      :data-source="orderList"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'orderNo'">
          <a-button type="link" @click="viewDetail(record)">
            {{ record.orderNo }}
          </a-button>
        </template>
        
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        
        <template v-if="column.key === 'paymentStatus'">
          <a-tag :color="getPaymentStatusColor(record.paymentStatus)">
            {{ getPaymentStatusText(record.paymentStatus) }}
          </a-tag>
        </template>
        
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="viewDetail(record)">
              <eye-outlined />
              查看详情
            </a-button>
            <a-button 
              v-if="record.status === 'pending'" 
              type="link" 
              size="small" 
              @click="processOrder(record)"
            >
              <play-circle-outlined />
              处理订单
            </a-button>
            <a-button 
              v-if="record.status !== 'completed' && record.status !== 'cancelled'" 
              type="link" 
              size="small" 
              danger 
              @click="cancelOrder(record)"
            >
              <close-outlined />
              取消订单
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 订单详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="订单详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentOrder">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="订单号">{{ currentOrder.orderNo }}</a-descriptions-item>
          <a-descriptions-item label="下单时间">{{ currentOrder.createTime }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ currentOrder.customerName }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ currentOrder.phone }}</a-descriptions-item>
          <a-descriptions-item label="订单状态">
            <a-tag :color="getStatusColor(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="支付状态">
            <a-tag :color="getPaymentStatusColor(currentOrder.paymentStatus)">
              {{ getPaymentStatusText(currentOrder.paymentStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="订单金额">¥{{ currentOrder.totalAmount }}</a-descriptions-item>
          <a-descriptions-item label="支付方式">{{ currentOrder.paymentMethod }}</a-descriptions-item>
          <a-descriptions-item label="备注信息" :span="2">{{ currentOrder.remark || '无' }}</a-descriptions-item>
        </a-descriptions>
        
        <div style="margin-top: 16px;">
          <h4>订单商品</h4>
          <a-table
            :columns="itemColumns"
            :data-source="currentOrder.items"
            :pagination="false"
            size="small"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  CheckOutlined,
  DownloadOutlined,
  SearchOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  CloseOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  DollarOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'OrdersToday',
  components: {
    ReloadOutlined,
    CheckOutlined,
    DownloadOutlined,
    SearchOutlined,
    EyeOutlined,
    PlayCircleOutlined,
    CloseOutlined,
    ShoppingCartOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    DollarOutlined
  },
  setup() {
    const loading = ref(false)
    const detailModalVisible = ref(false)
    const currentOrder = ref(null)
    const selectedRowKeys = ref([])
    
    // 今日统计数据
    const todayStats = ref({
      totalOrders: 156,
      pendingOrders: 23,
      completedOrders: 128,
      totalRevenue: 4580.50
    })
    
    // 筛选表单
    const filterForm = reactive({
      status: '',
      paymentStatus: '',
      orderNo: ''
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    })

    // 表格列配置
    const columns = [
      {
        title: '订单号',
        key: 'orderNo',
        width: 150
      },
      {
        title: '客户姓名',
        dataIndex: 'customerName',
        key: 'customerName',
        width: 100
      },
      {
        title: '联系电话',
        dataIndex: 'phone',
        key: 'phone',
        width: 120
      },
      {
        title: '订单金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        width: 100,
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '下单时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 150
      },
      {
        title: '订单状态',
        key: 'status',
        width: 100
      },
      {
        title: '支付状态',
        key: 'paymentStatus',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ]

    // 订单商品列配置
    const itemColumns = [
      {
        title: '商品名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '单价',
        dataIndex: 'price',
        key: 'price',
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity'
      },
      {
        title: '小计',
        dataIndex: 'subtotal',
        key: 'subtotal',
        customRender: ({ text }) => `¥${text}`
      }
    ]

    const orderList = ref([])

    // 计算属性
    const hasSelected = computed(() => selectedRowKeys.value.length > 0)

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }

    // 生成模拟数据
    const generateMockData = () => {
      const mockData = []
      const statuses = ['pending', 'preparing', 'ready', 'completed', 'cancelled']
      const paymentStatuses = ['unpaid', 'paid', 'refunded']
      const names = ['张三', '李四', '王五', '赵六', '钱七']
      const dishes = ['宫保鸡丁', '麻婆豆腐', '红烧肉', '西红柿鸡蛋', '青椒土豆丝']
      
      for (let i = 1; i <= 50; i++) {
        const items = []
        const itemCount = Math.floor(Math.random() * 3) + 1
        let totalAmount = 0
        
        for (let j = 0; j < itemCount; j++) {
          const price = (Math.random() * 20 + 10).toFixed(2)
          const quantity = Math.floor(Math.random() * 3) + 1
          const subtotal = (price * quantity).toFixed(2)
          totalAmount += parseFloat(subtotal)
          
          items.push({
            name: dishes[Math.floor(Math.random() * dishes.length)],
            price: price,
            quantity: quantity,
            subtotal: subtotal
          })
        }
        
        mockData.push({
          id: i,
          orderNo: `ORD${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(i).padStart(4, '0')}`,
          customerName: names[Math.floor(Math.random() * names.length)],
          phone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
          totalAmount: totalAmount.toFixed(2),
          createTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleString(),
          status: statuses[Math.floor(Math.random() * statuses.length)],
          paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
          paymentMethod: ['微信支付', '支付宝', '现金'][Math.floor(Math.random() * 3)],
          remark: Math.random() > 0.5 ? '少辣' : '',
          items: items
        })
      }
      return mockData
    }

    // 状态相关方法
    const getStatusColor = (status) => {
      const colors = {
        pending: 'orange',
        preparing: 'blue',
        ready: 'cyan',
        completed: 'green',
        cancelled: 'red'
      }
      return colors[status] || 'default'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待处理',
        preparing: '制作中',
        ready: '待取餐',
        completed: '已完成',
        cancelled: '已取消'
      }
      return texts[status] || '未知'
    }

    const getPaymentStatusColor = (status) => {
      const colors = {
        unpaid: 'red',
        paid: 'green',
        refunded: 'orange'
      }
      return colors[status] || 'default'
    }

    const getPaymentStatusText = (status) => {
      const texts = {
        unpaid: '未支付',
        paid: '已支付',
        refunded: '已退款'
      }
      return texts[status] || '未知'
    }

    // 数据加载
    const loadData = async () => {
      loading.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const mockData = generateMockData()
        let filteredData = mockData

        // 应用筛选条件
        if (filterForm.status) {
          filteredData = filteredData.filter(item => item.status === filterForm.status)
        }
        if (filterForm.paymentStatus) {
          filteredData = filteredData.filter(item => item.paymentStatus === filterForm.paymentStatus)
        }
        if (filterForm.orderNo) {
          filteredData = filteredData.filter(item => 
            item.orderNo.toLowerCase().includes(filterForm.orderNo.toLowerCase())
          )
        }

        pagination.total = filteredData.length
        const start = (pagination.current - 1) * pagination.pageSize
        const end = start + pagination.pageSize
        orderList.value = filteredData.slice(start, end)
      } catch (error) {
        message.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    // 操作方法
    const refreshData = () => {
      loadData()
    }

    const handleFilter = () => {
      pagination.current = 1
      loadData()
    }

    const resetFilter = () => {
      Object.assign(filterForm, {
        status: '',
        paymentStatus: '',
        orderNo: ''
      })
      handleFilter()
    }

    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadData()
    }

    const viewDetail = (record) => {
      currentOrder.value = record
      detailModalVisible.value = true
    }

    const processOrder = async (record) => {
      try {
        loading.value = true
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        record.status = 'preparing'
        message.success('订单处理成功!')
      } catch (error) {
        message.error('操作失败')
      } finally {
        loading.value = false
      }
    }

    const cancelOrder = async (record) => {
      try {
        loading.value = true
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        record.status = 'cancelled'
        message.success('订单取消成功!')
      } catch (error) {
        message.error('操作失败')
      } finally {
        loading.value = false
      }
    }

    const batchProcess = async () => {
      try {
        loading.value = true
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        message.success(`批量处理 ${selectedRowKeys.value.length} 个订单`)
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量操作失败')
      } finally {
        loading.value = false
      }
    }

    const exportData = () => {
      message.info('导出功能开发中...')
    }

    onMounted(() => {
      loadData()
    })

    return {
      loading,
      detailModalVisible,
      currentOrder,
      selectedRowKeys,
      todayStats,
      filterForm,
      pagination,
      columns,
      itemColumns,
      orderList,
      hasSelected,
      rowSelection,
      getStatusColor,
      getStatusText,
      getPaymentStatusColor,
      getPaymentStatusText,
      refreshData,
      handleFilter,
      resetFilter,
      handleTableChange,
      viewDetail,
      processOrder,
      cancelOrder,
      batchProcess,
      exportData
    }
  }
}
</script>

<style scoped>
.orders-today {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

.action-buttons {
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
}

.stat-card {
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 24px;
}
</style>
