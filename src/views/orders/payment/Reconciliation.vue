<template>
  <div class="orders-payment-reconciliation">
    <div class="page-title">对账管理</div>
    <placeholder-page 
      title="对账管理"
      description="对账管理功能正在开发中"
      icon="ReconciliationOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersPaymentReconciliation',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '自动对账', description: '系统自动对账和差异识别' },
      { title: '手工对账', description: '手工对账和差异调整功能' },
      { title: '对账报表', description: '详细的对账报表和汇总统计' },
      { title: '差异处理', description: '对账差异的原因分析和处理' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-payment-reconciliation {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
