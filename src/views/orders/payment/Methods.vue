<template>
  <div class="orders-payment-methods">
    <div class="page-title">支付方式设置</div>
    <placeholder-page 
      title="支付方式设置"
      description="支付方式设置功能正在开发中"
      icon="SettingOutlined"
      action-text="查看演示"
      :features="features"
    />
  </div>
</template>

<script>
import PlaceholderPage from '@/components/PlaceholderPage.vue'

export default {
  name: 'OrdersPaymentMethods',
  components: {
    PlaceholderPage
  },
  setup() {
    const features = [
      { title: '支付渠道配置', description: '微信、支付宝等支付渠道配置' },
      { title: '支付参数设置', description: '支付接口参数和密钥配置' },
      { title: '支付限额管理', description: '不同支付方式的限额设置' },
      { title: '支付手续费配置', description: '各支付渠道手续费率配置' }
    ]

    return {
      features
    }
  }
}
</script>

<style scoped>
.orders-payment-methods {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}
</style>
