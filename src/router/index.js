import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/Layout.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/overview',
    meta: { title: '首页仪表板', icon: 'HomeOutlined' },
    children: [
      {
        path: 'overview',
        name: 'DashboardOverview',
        component: () => import('@/views/dashboard/Overview.vue'),
        meta: { title: '营业概览', icon: 'BarChartOutlined' }
      },
      {
        path: 'monitor',
        name: 'DashboardMonitor',
        component: () => import('@/views/dashboard/Monitor.vue'),
        meta: { title: '实时数据监控', icon: 'LineChartOutlined' }
      },
      {
        path: 'tasks',
        name: 'DashboardTasks',
        component: () => import('@/views/dashboard/Tasks.vue'),
        meta: { title: '待处理事项', icon: 'AlertOutlined' }
      },
      {
        path: 'shortcuts',
        name: 'DashboardShortcuts',
        component: () => import('@/views/dashboard/Shortcuts.vue'),
        meta: { title: '快捷操作入口', icon: 'ThunderboltOutlined' }
      }
    ]
  },
  {
    path: '/dishes',
    component: Layout,
    redirect: '/dishes/list',
    meta: { title: '菜品管理', icon: 'ShopOutlined' },
    children: [
      {
        path: 'list',
        name: 'DishesList',
        component: () => import('@/views/dishes/List.vue'),
        meta: { title: '菜品列表', icon: 'UnorderedListOutlined' }
      },
      {
        path: 'add',
        name: 'DishesAdd',
        component: () => import('@/views/dishes/Add.vue'),
        meta: { title: '新增菜品', icon: 'PlusOutlined' }
      },
      {
        path: 'edit/:id',
        name: 'DishesEdit',
        component: () => import('@/views/dishes/Edit.vue'),
        meta: { title: '菜品编辑', icon: 'EditOutlined' },
        hidden: true
      },
      {
        path: 'audit',
        name: 'DishesAudit',
        component: () => import('@/views/dishes/Audit.vue'),
        meta: { title: '菜品审核', icon: 'AuditOutlined' }
      },
      {
        path: 'categories',
        name: 'DishesCategories',
        component: () => import('@/views/dishes/Categories.vue'),
        meta: { title: '分类管理', icon: 'FolderOutlined' }
      },
      {
        path: 'nutrition',
        name: 'DishesNutrition',
        component: () => import('@/views/dishes/Nutrition.vue'),
        meta: { title: '营养成分管理', icon: 'HeartOutlined' }
      },
      {
        path: 'pricing',
        name: 'DishesPricing',
        component: () => import('@/views/dishes/Pricing.vue'),
        meta: { title: '价格管理', icon: 'DollarOutlined' }
      }
    ]
  },
  {
    path: '/orders',
    component: Layout,
    redirect: '/orders/today',
    meta: { title: '订餐管理', icon: 'ShoppingCartOutlined' },
    children: [
      {
        path: 'today',
        name: 'OrdersToday',
        component: () => import('@/views/orders/OrdersToday.vue'),
        meta: { title: '今日订单', icon: 'CalendarOutlined' }
      },
      {
        path: 'history',
        name: 'OrdersHistory',
        component: () => import('@/views/orders/OrdersHistory.vue'),
        meta: { title: '历史订单', icon: 'HistoryOutlined' }
      },
      {
        path: 'tracking',
        name: 'OrdersTracking',
        component: () => import('@/views/orders/OrdersTracking.vue'),
        meta: { title: '订单状态跟踪', icon: 'EyeOutlined' }
      },
      {
        path: 'refunds',
        name: 'OrdersRefunds',
        component: () => import('@/views/orders/OrdersRefunds.vue'),
        meta: { title: '退款处理', icon: 'RollbackOutlined' }
      },
      {
        path: 'queue',
        name: 'OrdersQueue',
        component: () => import('@/views/orders/Queue.vue'),
        meta: { title: '排队取餐管理', icon: 'TeamOutlined' }
      },
      {
        path: 'delivery',
        name: 'OrdersDelivery',
        component: () => import('@/views/orders/Delivery.vue'),
        meta: { title: '配送管理', icon: 'CarOutlined' }
      },
      {
        path: 'payment',
        name: 'OrdersPayment',
        component: () => import('@/views/orders/Payment.vue'),
        meta: { title: '支付管理', icon: 'CreditCardOutlined' }
      }
    ]
  },
  {
    path: '/inventory',
    component: Layout,
    redirect: '/inventory/ingredients',
    meta: { title: '库存管理', icon: 'InboxOutlined' },
    children: [
      {
        path: 'ingredients',
        name: 'InventoryIngredients',
        component: () => import('@/views/inventory/Ingredients.vue'),
        meta: { title: '食材管理', icon: 'AppleOutlined' }
      },
      {
        path: 'inbound',
        name: 'InventoryInbound',
        component: () => import('@/views/inventory/Inbound.vue'),
        meta: { title: '入库管理', icon: 'ImportOutlined' }
      },
      {
        path: 'outbound',
        name: 'InventoryOutbound',
        component: () => import('@/views/inventory/Outbound.vue'),
        meta: { title: '出库管理', icon: 'ExportOutlined' }
      },
      {
        path: 'monitor',
        name: 'InventoryMonitor',
        component: () => import('@/views/inventory/Monitor.vue'),
        meta: { title: '库存监控', icon: 'MonitorOutlined' }
      }
    ]
  },
  {
    path: '/procurement',
    component: Layout,
    redirect: '/procurement/suppliers',
    meta: { title: '采购管理', icon: 'ShoppingOutlined' },
    children: [
      {
        path: 'suppliers',
        name: 'ProcurementSuppliers',
        component: () => import('@/views/procurement/Suppliers.vue'),
        meta: { title: '供应商管理', icon: 'BankOutlined' }
      },
      {
        path: 'planning',
        name: 'ProcurementPlanning',
        component: () => import('@/views/procurement/Planning.vue'),
        meta: { title: '采购计划', icon: 'ScheduleOutlined' }
      },
      {
        path: 'orders',
        name: 'ProcurementOrders',
        component: () => import('@/views/procurement/Orders.vue'),
        meta: { title: '采购订单', icon: 'FileTextOutlined' }
      },
      {
        path: 'analysis',
        name: 'ProcurementAnalysis',
        component: () => import('@/views/procurement/Analysis.vue'),
        meta: { title: '采购分析', icon: 'BarChartOutlined' }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/revenue',
    meta: { title: '财务管理', icon: 'AccountBookOutlined' },
    children: [
      {
        path: 'revenue',
        name: 'FinanceRevenue',
        component: () => import('@/views/finance/Revenue.vue'),
        meta: { title: '收入管理', icon: 'RiseOutlined' }
      },
      {
        path: 'cost',
        name: 'FinanceCost',
        component: () => import('@/views/finance/Cost.vue'),
        meta: { title: '成本管理', icon: 'FallOutlined' }
      },
      {
        path: 'profit',
        name: 'FinanceProfit',
        component: () => import('@/views/finance/Profit.vue'),
        meta: { title: '利润分析', icon: 'LineChartOutlined' }
      },
      {
        path: 'reports',
        name: 'FinanceReports',
        component: () => import('@/views/finance/Reports.vue'),
        meta: { title: '财务报表', icon: 'FileExcelOutlined' }
      }
    ]
  },
  {
    path: '/nutrition',
    component: Layout,
    redirect: '/nutrition/meal-planning',
    meta: { title: '营养健康', icon: 'HeartOutlined' },
    children: [
      {
        path: 'meal-planning',
        name: 'NutritionMealPlanning',
        component: () => import('@/views/nutrition/MealPlanning.vue'),
        meta: { title: '营养配餐', icon: 'AppleOutlined' }
      },
      {
        path: 'health',
        name: 'NutritionHealth',
        component: () => import('@/views/nutrition/Health.vue'),
        meta: { title: '健康管理', icon: 'UserOutlined' }
      },
      {
        path: 'analysis',
        name: 'NutritionAnalysis',
        component: () => import('@/views/nutrition/Analysis.vue'),
        meta: { title: '营养分析', icon: 'PieChartOutlined' }
      },
      {
        path: 'safety',
        name: 'NutritionSafety',
        component: () => import('@/views/nutrition/Safety.vue'),
        meta: { title: '食品安全', icon: 'SafetyCertificateOutlined' }
      }
    ]
  },
  {
    path: '/analytics',
    component: Layout,
    redirect: '/analytics/sales',
    meta: { title: '数据分析', icon: 'BarChartOutlined' },
    children: [
      {
        path: 'sales',
        name: 'AnalyticsSales',
        component: () => import('@/views/analytics/Sales.vue'),
        meta: { title: '销售分析', icon: 'LineChartOutlined' }
      },
      {
        path: 'users',
        name: 'AnalyticsUsers',
        component: () => import('@/views/analytics/Users.vue'),
        meta: { title: '用户分析', icon: 'UserOutlined' }
      },
      {
        path: 'operations',
        name: 'AnalyticsOperations',
        component: () => import('@/views/analytics/Operations.vue'),
        meta: { title: '运营分析', icon: 'SettingOutlined' }
      },
      {
        path: 'forecast',
        name: 'AnalyticsForecast',
        component: () => import('@/views/analytics/Forecast.vue'),
        meta: { title: '预测分析', icon: 'RocketOutlined' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: { title: '系统管理', icon: 'SettingOutlined' },
    children: [
      {
        path: 'users',
        name: 'SystemUsers',
        component: () => import('@/views/system/Users.vue'),
        meta: { title: '用户管理', icon: 'UserOutlined' }
      },
      {
        path: 'roles',
        name: 'SystemRoles',
        component: () => import('@/views/system/Roles.vue'),
        meta: { title: '角色权限', icon: 'TeamOutlined' }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/Config.vue'),
        meta: { title: '系统配置', icon: 'ToolOutlined' }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/system/Logs.vue'),
        meta: { title: '日志管理', icon: 'FileTextOutlined' }
      },
      {
        path: 'data',
        name: 'SystemData',
        component: () => import('@/views/system/Data.vue'),
        meta: { title: '数据管理', icon: 'DatabaseOutlined' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router