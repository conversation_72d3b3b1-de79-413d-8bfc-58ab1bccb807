import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/Layout.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  // 🏠 首页仪表板
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/overview',
    meta: { title: '首页仪表板', icon: 'HomeOutlined' },
    children: [
      {
        path: 'overview',
        name: 'DashboardOverview',
        component: () => import('@/views/dashboard/Overview.vue'),
        meta: { title: '营业概览', icon: 'BarChartOutlined' }
      },
      {
        path: 'monitor',
        name: 'DashboardMonitor',
        component: () => import('@/views/dashboard/Monitor.vue'),
        meta: { title: '实时数据监控', icon: 'LineChartOutlined' }
      },
      {
        path: 'pending',
        name: 'DashboardPending',
        component: () => import('@/views/dashboard/Pending.vue'),
        meta: { title: '待处理事项', icon: 'ExclamationCircleOutlined' }
      },
      {
        path: 'shortcuts',
        name: 'DashboardShortcuts',
        component: () => import('@/views/dashboard/Shortcuts.vue'),
        meta: { title: '快捷操作入口', icon: 'ThunderboltOutlined' }
      }
    ]
  },
  // 🍽️ 菜品管理
  {
    path: '/dishes',
    component: Layout,
    redirect: '/dishes/info/list',
    meta: { title: '菜品管理', icon: 'ShopOutlined' },
    children: [
      // 📋 菜品信息管理
      {
        path: 'info',
        name: 'DishesInfo',
        redirect: '/dishes/info/list',
        meta: { title: '菜品信息管理', icon: 'FileTextOutlined' },
        children: [
          {
            path: 'list',
            name: 'DishesInfoList',
            component: () => import('@/views/dishes/List.vue'),
            meta: { title: '菜品列表', icon: 'UnorderedListOutlined' }
          },
          {
            path: 'add',
            name: 'DishesInfoAdd',
            component: () => import('@/views/dishes/Add.vue'),
            meta: { title: '新增菜品', icon: 'PlusOutlined' }
          },
          {
            path: 'edit/:id?',
            name: 'DishesInfoEdit',
            component: () => import('@/views/dishes/Edit.vue'),
            meta: { title: '菜品编辑', icon: 'EditOutlined' },
            hidden: true
          },
          {
            path: 'audit',
            name: 'DishesInfoAudit',
            component: () => import('@/views/dishes/Audit.vue'),
            meta: { title: '菜品审核', icon: 'AuditOutlined' }
          }
        ]
      },
      // 🗂️ 菜品分类管理
      {
        path: 'category',
        name: 'DishesCategory',
        redirect: '/dishes/category/list',
        meta: { title: '菜品分类管理', icon: 'FolderOutlined' },
        children: [
          {
            path: 'list',
            name: 'DishesCategoryList',
            component: () => import('@/views/dishes/category/List.vue'),
            meta: { title: '分类列表', icon: 'UnorderedListOutlined' }
          },
          {
            path: 'settings',
            name: 'DishesCategorySettings',
            component: () => import('@/views/dishes/category/Settings.vue'),
            meta: { title: '分类设置', icon: 'SettingOutlined' }
          }
        ]
      },
      // 🥗 营养成分管理
      {
        path: 'nutrition',
        name: 'DishesNutrition',
        redirect: '/dishes/nutrition/input',
        meta: { title: '营养成分管理', icon: 'AppleOutlined' },
        children: [
          {
            path: 'input',
            name: 'DishesNutritionInput',
            component: () => import('@/views/dishes/nutrition/Input.vue'),
            meta: { title: '营养信息录入', icon: 'EditOutlined' }
          },
          {
            path: 'standards',
            name: 'DishesNutritionStandards',
            component: () => import('@/views/dishes/nutrition/Standards.vue'),
            meta: { title: '营养标准设置', icon: 'SettingOutlined' }
          }
        ]
      },
      // 💰 价格管理
      {
        path: 'pricing',
        name: 'DishesPricing',
        redirect: '/dishes/pricing/strategy',
        meta: { title: '价格管理', icon: 'DollarOutlined' },
        children: [
          {
            path: 'strategy',
            name: 'DishesPricingStrategy',
            component: () => import('@/views/dishes/pricing/Strategy.vue'),
            meta: { title: '定价策略', icon: 'CalculatorOutlined' }
          },
          {
            path: 'history',
            name: 'DishesPricingHistory',
            component: () => import('@/views/dishes/pricing/History.vue'),
            meta: { title: '价格调整记录', icon: 'HistoryOutlined' }
          },
          {
            path: 'promotion',
            name: 'DishesPricingPromotion',
            component: () => import('@/views/dishes/pricing/Promotion.vue'),
            meta: { title: '促销活动设置', icon: 'GiftOutlined' }
          }
        ]
      }
    ]
  },
  // 🛒 订餐管理
  {
    path: '/orders',
    component: Layout,
    redirect: '/orders/management/today',
    meta: { title: '订餐管理', icon: 'ShoppingCartOutlined' },
    children: [
      // 📝 订单管理
      {
        path: 'management',
        name: 'OrdersManagement',
        redirect: '/orders/management/today',
        meta: { title: '订单管理', icon: 'FileTextOutlined' },
        children: [
          {
            path: 'today',
            name: 'OrdersManagementToday',
            component: () => import('@/views/orders/OrdersToday.vue'),
            meta: { title: '今日订单', icon: 'CalendarOutlined' }
          },
          {
            path: 'history',
            name: 'OrdersManagementHistory',
            component: () => import('@/views/orders/OrdersHistory.vue'),
            meta: { title: '历史订单', icon: 'HistoryOutlined' }
          },
          {
            path: 'tracking',
            name: 'OrdersManagementTracking',
            component: () => import('@/views/orders/OrdersTracking.vue'),
            meta: { title: '订单状态跟踪', icon: 'EyeOutlined' }
          },
          {
            path: 'refunds',
            name: 'OrdersManagementRefunds',
            component: () => import('@/views/orders/OrdersRefunds.vue'),
            meta: { title: '退款处理', icon: 'RollbackOutlined' }
          }
        ]
      },
      // 🔢 排队取餐管理
      {
        path: 'queue',
        name: 'OrdersQueue',
        redirect: '/orders/queue/status',
        meta: { title: '排队取餐管理', icon: 'TeamOutlined' },
        children: [
          {
            path: 'status',
            name: 'OrdersQueueStatus',
            component: () => import('@/views/orders/queue/Status.vue'),
            meta: { title: '实时排队状况', icon: 'EyeOutlined' }
          },
          {
            path: 'numbers',
            name: 'OrdersQueueNumbers',
            component: () => import('@/views/orders/queue/Numbers.vue'),
            meta: { title: '取餐号码管理', icon: 'NumberOutlined' }
          },
          {
            path: 'waiting',
            name: 'OrdersQueueWaiting',
            component: () => import('@/views/orders/queue/Waiting.vue'),
            meta: { title: '等待时间预估', icon: 'ClockCircleOutlined' }
          }
        ]
      },
      // 🚚 配送管理
      {
        path: 'delivery',
        name: 'OrdersDelivery',
        redirect: '/orders/delivery/orders',
        meta: { title: '配送管理', icon: 'CarOutlined' },
        children: [
          {
            path: 'orders',
            name: 'OrdersDeliveryOrders',
            component: () => import('@/views/orders/delivery/Orders.vue'),
            meta: { title: '配送订单', icon: 'FileTextOutlined' }
          },
          {
            path: 'staff',
            name: 'OrdersDeliveryStaff',
            component: () => import('@/views/orders/delivery/Staff.vue'),
            meta: { title: '配送员管理', icon: 'UserOutlined' }
          },
          {
            path: 'routes',
            name: 'OrdersDeliveryRoutes',
            component: () => import('@/views/orders/delivery/Routes.vue'),
            meta: { title: '配送路线规划', icon: 'EnvironmentOutlined' }
          }
        ]
      },
      // 💳 支付管理
      {
        path: 'payment',
        name: 'OrdersPayment',
        redirect: '/orders/payment/methods',
        meta: { title: '支付管理', icon: 'CreditCardOutlined' },
        children: [
          {
            path: 'methods',
            name: 'OrdersPaymentMethods',
            component: () => import('@/views/orders/payment/Methods.vue'),
            meta: { title: '支付方式设置', icon: 'SettingOutlined' }
          },
          {
            path: 'records',
            name: 'OrdersPaymentRecords',
            component: () => import('@/views/orders/payment/Records.vue'),
            meta: { title: '交易记录', icon: 'HistoryOutlined' }
          },
          {
            path: 'reconciliation',
            name: 'OrdersPaymentReconciliation',
            component: () => import('@/views/orders/payment/Reconciliation.vue'),
            meta: { title: '对账管理', icon: 'ReconciliationOutlined' }
          }
        ]
      }
    ]
  },
  // 📦 库存管理
  {
    path: '/inventory',
    component: Layout,
    redirect: '/inventory/ingredients/archive',
    meta: { title: '库存管理', icon: 'InboxOutlined' },
    children: [
      // 🥬 食材管理
      {
        path: 'ingredients',
        name: 'InventoryIngredients',
        redirect: '/inventory/ingredients/archive',
        meta: { title: '食材管理', icon: 'AppleOutlined' },
        children: [
          {
            path: 'archive',
            name: 'InventoryIngredientsArchive',
            component: () => import('@/views/inventory/ingredients/Archive.vue'),
            meta: { title: '食材档案', icon: 'FileTextOutlined' }
          },
          {
            path: 'category',
            name: 'InventoryIngredientsCategory',
            component: () => import('@/views/inventory/ingredients/Category.vue'),
            meta: { title: '食材分类', icon: 'FolderOutlined' }
          },
          {
            path: 'expiry',
            name: 'InventoryIngredientsExpiry',
            component: () => import('@/views/inventory/ingredients/Expiry.vue'),
            meta: { title: '保质期管理', icon: 'ClockCircleOutlined' }
          }
        ]
      },
      // 📥 入库管理
      {
        path: 'inbound',
        name: 'InventoryInbound',
        redirect: '/inventory/inbound/register',
        meta: { title: '入库管理', icon: 'ImportOutlined' },
        children: [
          {
            path: 'register',
            name: 'InventoryInboundRegister',
            component: () => import('@/views/inventory/inbound/Register.vue'),
            meta: { title: '入库登记', icon: 'EditOutlined' }
          },
          {
            path: 'audit',
            name: 'InventoryInboundAudit',
            component: () => import('@/views/inventory/inbound/Audit.vue'),
            meta: { title: '入库审核', icon: 'AuditOutlined' }
          },
          {
            path: 'quality',
            name: 'InventoryInboundQuality',
            component: () => import('@/views/inventory/inbound/Quality.vue'),
            meta: { title: '质检记录', icon: 'SafetyCertificateOutlined' }
          }
        ]
      },
      // 📤 出库管理
      {
        path: 'outbound',
        name: 'InventoryOutbound',
        redirect: '/inventory/outbound/apply',
        meta: { title: '出库管理', icon: 'ExportOutlined' },
        children: [
          {
            path: 'apply',
            name: 'InventoryOutboundApply',
            component: () => import('@/views/inventory/outbound/Apply.vue'),
            meta: { title: '出库申请', icon: 'FormOutlined' }
          },
          {
            path: 'audit',
            name: 'InventoryOutboundAudit',
            component: () => import('@/views/inventory/outbound/Audit.vue'),
            meta: { title: '出库审核', icon: 'AuditOutlined' }
          },
          {
            path: 'usage',
            name: 'InventoryOutboundUsage',
            component: () => import('@/views/inventory/outbound/Usage.vue'),
            meta: { title: '用料统计', icon: 'BarChartOutlined' }
          }
        ]
      },
      // 👁️ 库存监控
      {
        path: 'monitor',
        name: 'InventoryMonitor',
        redirect: '/inventory/monitor/query',
        meta: { title: '库存监控', icon: 'MonitorOutlined' },
        children: [
          {
            path: 'query',
            name: 'InventoryMonitorQuery',
            component: () => import('@/views/inventory/monitor/Query.vue'),
            meta: { title: '库存查询', icon: 'SearchOutlined' }
          },
          {
            path: 'alert',
            name: 'InventoryMonitorAlert',
            component: () => import('@/views/inventory/monitor/Alert.vue'),
            meta: { title: '库存预警', icon: 'AlertOutlined' }
          },
          {
            path: 'stocktaking',
            name: 'InventoryMonitorStocktaking',
            component: () => import('@/views/inventory/monitor/Stocktaking.vue'),
            meta: { title: '盘点管理', icon: 'CheckSquareOutlined' }
          },
          {
            path: 'loss',
            name: 'InventoryMonitorLoss',
            component: () => import('@/views/inventory/monitor/Loss.vue'),
            meta: { title: '损耗统计', icon: 'FallOutlined' }
          }
        ]
      }
    ]
  },
  {
    path: '/procurement',
    component: Layout,
    redirect: '/procurement/suppliers',
    meta: { title: '采购管理', icon: 'ShoppingOutlined' },
    children: [
      {
        path: 'suppliers',
        name: 'ProcurementSuppliers',
        component: () => import('@/views/procurement/Suppliers.vue'),
        meta: { title: '供应商管理', icon: 'BankOutlined' }
      },
      {
        path: 'planning',
        name: 'ProcurementPlanning',
        component: () => import('@/views/procurement/Planning.vue'),
        meta: { title: '采购计划', icon: 'ScheduleOutlined' }
      },
      {
        path: 'orders',
        name: 'ProcurementOrders',
        component: () => import('@/views/procurement/Orders.vue'),
        meta: { title: '采购订单', icon: 'FileTextOutlined' }
      },
      {
        path: 'analysis',
        name: 'ProcurementAnalysis',
        component: () => import('@/views/procurement/Analysis.vue'),
        meta: { title: '采购分析', icon: 'BarChartOutlined' }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/revenue',
    meta: { title: '财务管理', icon: 'AccountBookOutlined' },
    children: [
      {
        path: 'revenue',
        name: 'FinanceRevenue',
        component: () => import('@/views/finance/Revenue.vue'),
        meta: { title: '收入管理', icon: 'RiseOutlined' }
      },
      {
        path: 'cost',
        name: 'FinanceCost',
        component: () => import('@/views/finance/Cost.vue'),
        meta: { title: '成本管理', icon: 'FallOutlined' }
      },
      {
        path: 'profit',
        name: 'FinanceProfit',
        component: () => import('@/views/finance/Profit.vue'),
        meta: { title: '利润分析', icon: 'LineChartOutlined' }
      },
      {
        path: 'reports',
        name: 'FinanceReports',
        component: () => import('@/views/finance/Reports.vue'),
        meta: { title: '财务报表', icon: 'FileExcelOutlined' }
      }
    ]
  },
  {
    path: '/nutrition',
    component: Layout,
    redirect: '/nutrition/meal-planning',
    meta: { title: '营养健康', icon: 'HeartOutlined' },
    children: [
      {
        path: 'meal-planning',
        name: 'NutritionMealPlanning',
        component: () => import('@/views/nutrition/MealPlanning.vue'),
        meta: { title: '营养配餐', icon: 'AppleOutlined' }
      },
      {
        path: 'health',
        name: 'NutritionHealth',
        component: () => import('@/views/nutrition/Health.vue'),
        meta: { title: '健康管理', icon: 'UserOutlined' }
      },
      {
        path: 'analysis',
        name: 'NutritionAnalysis',
        component: () => import('@/views/nutrition/Analysis.vue'),
        meta: { title: '营养分析', icon: 'PieChartOutlined' }
      },
      {
        path: 'safety',
        name: 'NutritionSafety',
        component: () => import('@/views/nutrition/Safety.vue'),
        meta: { title: '食品安全', icon: 'SafetyCertificateOutlined' }
      }
    ]
  },
  {
    path: '/analytics',
    component: Layout,
    redirect: '/analytics/sales',
    meta: { title: '数据分析', icon: 'BarChartOutlined' },
    children: [
      {
        path: 'sales',
        name: 'AnalyticsSales',
        component: () => import('@/views/analytics/Sales.vue'),
        meta: { title: '销售分析', icon: 'LineChartOutlined' }
      },
      {
        path: 'users',
        name: 'AnalyticsUsers',
        component: () => import('@/views/analytics/Users.vue'),
        meta: { title: '用户分析', icon: 'UserOutlined' }
      },
      {
        path: 'operations',
        name: 'AnalyticsOperations',
        component: () => import('@/views/analytics/Operations.vue'),
        meta: { title: '运营分析', icon: 'SettingOutlined' }
      },
      {
        path: 'forecast',
        name: 'AnalyticsForecast',
        component: () => import('@/views/analytics/Forecast.vue'),
        meta: { title: '预测分析', icon: 'RocketOutlined' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: { title: '系统管理', icon: 'SettingOutlined' },
    children: [
      {
        path: 'users',
        name: 'SystemUsers',
        component: () => import('@/views/system/Users.vue'),
        meta: { title: '用户管理', icon: 'UserOutlined' }
      },
      {
        path: 'roles',
        name: 'SystemRoles',
        component: () => import('@/views/system/Roles.vue'),
        meta: { title: '角色权限', icon: 'TeamOutlined' }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/Config.vue'),
        meta: { title: '系统配置', icon: 'ToolOutlined' }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/system/Logs.vue'),
        meta: { title: '日志管理', icon: 'FileTextOutlined' }
      },
      {
        path: 'data',
        name: 'SystemData',
        component: () => import('@/views/system/Data.vue'),
        meta: { title: '数据管理', icon: 'DatabaseOutlined' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router