import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/Layout.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  // 🏠 首页仪表板
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/overview',
    meta: { title: '首页仪表板', icon: 'HomeOutlined' },
    children: [
      {
        path: 'overview',
        name: 'DashboardOverview',
        component: () => import('@/views/dashboard/Overview.vue'),
        meta: { title: '营业概览', icon: 'BarChartOutlined' }
      },
      {
        path: 'monitor',
        name: 'DashboardMonitor',
        component: () => import('@/views/dashboard/Monitor.vue'),
        meta: { title: '实时数据监控', icon: 'LineChartOutlined' }
      },
      {
        path: 'pending',
        name: 'DashboardPending',
        component: () => import('@/views/dashboard/Pending.vue'),
        meta: { title: '待处理事项', icon: 'ExclamationCircleOutlined' }
      },
      {
        path: 'shortcuts',
        name: 'DashboardShortcuts',
        component: () => import('@/views/dashboard/Shortcuts.vue'),
        meta: { title: '快捷操作入口', icon: 'ThunderboltOutlined' }
      }
    ]
  },
  // 🍽️ 菜品管理
  {
    path: '/dishes',
    component: Layout,
    redirect: '/dishes/list',
    meta: { title: '菜品管理', icon: 'ShopOutlined' },
    children: [
      {
        path: 'list',
        name: 'DishesList',
        component: () => import('@/views/dishes/List.vue'),
        meta: { title: '菜品列表', icon: 'UnorderedListOutlined' }
      },
      {
        path: 'add',
        name: 'DishesAdd',
        component: () => import('@/views/dishes/Add.vue'),
        meta: { title: '新增菜品', icon: 'PlusOutlined' }
      },
      {
        path: 'edit/:id?',
        name: 'DishesEdit',
        component: () => import('@/views/dishes/Edit.vue'),
        meta: { title: '菜品编辑', icon: 'EditOutlined' },
        hidden: true
      },
      {
        path: 'audit',
        name: 'DishesAudit',
        component: () => import('@/views/dishes/Audit.vue'),
        meta: { title: '菜品审核', icon: 'AuditOutlined' }
      },
      {
        path: 'category-list',
        name: 'DishesCategoryList',
        component: () => import('@/views/dishes/category/List.vue'),
        meta: { title: '分类列表', icon: 'FolderOutlined' }
      },
      {
        path: 'category-settings',
        name: 'DishesCategorySettings',
        component: () => import('@/views/dishes/category/Settings.vue'),
        meta: { title: '分类设置', icon: 'SettingOutlined' }
      },
      {
        path: 'nutrition-input',
        name: 'DishesNutritionInput',
        component: () => import('@/views/dishes/nutrition/Input.vue'),
        meta: { title: '营养信息录入', icon: 'HeartOutlined' }
      },
      {
        path: 'nutrition-standards',
        name: 'DishesNutritionStandards',
        component: () => import('@/views/dishes/nutrition/Standards.vue'),
        meta: { title: '营养标准设置', icon: 'ExperimentOutlined' }
      },
      {
        path: 'pricing-strategy',
        name: 'DishesPricingStrategy',
        component: () => import('@/views/dishes/pricing/Strategy.vue'),
        meta: { title: '定价策略', icon: 'CalculatorOutlined' }
      },
      {
        path: 'pricing-history',
        name: 'DishesPricingHistory',
        component: () => import('@/views/dishes/pricing/History.vue'),
        meta: { title: '价格调整记录', icon: 'HistoryOutlined' }
      },
      {
        path: 'pricing-promotion',
        name: 'DishesPricingPromotion',
        component: () => import('@/views/dishes/pricing/Promotion.vue'),
        meta: { title: '促销活动设置', icon: 'GiftOutlined' }
      }
    ]
  },
  // 🛒 订餐管理
  {
    path: '/orders',
    component: Layout,
    redirect: '/orders/today',
    meta: { title: '订餐管理', icon: 'ShoppingCartOutlined' },
    children: [
      {
        path: 'today',
        name: 'OrdersToday',
        component: () => import('@/views/orders/OrdersToday.vue'),
        meta: { title: '今日订单', icon: 'CalendarOutlined' }
      },
      {
        path: 'history',
        name: 'OrdersHistory',
        component: () => import('@/views/orders/OrdersHistory.vue'),
        meta: { title: '历史订单', icon: 'HistoryOutlined' }
      },
      {
        path: 'tracking',
        name: 'OrdersTracking',
        component: () => import('@/views/orders/OrdersTracking.vue'),
        meta: { title: '订单状态跟踪', icon: 'EyeOutlined' }
      },
      {
        path: 'refunds',
        name: 'OrdersRefunds',
        component: () => import('@/views/orders/OrdersRefunds.vue'),
        meta: { title: '退款处理', icon: 'RollbackOutlined' }
      },
      {
        path: 'queue-status',
        name: 'OrdersQueueStatus',
        component: () => import('@/views/orders/queue/Status.vue'),
        meta: { title: '实时排队状况', icon: 'TeamOutlined' }
      },
      {
        path: 'queue-numbers',
        name: 'OrdersQueueNumbers',
        component: () => import('@/views/orders/queue/Numbers.vue'),
        meta: { title: '取餐号码管理', icon: 'NumberOutlined' }
      },
      {
        path: 'queue-waiting',
        name: 'OrdersQueueWaiting',
        component: () => import('@/views/orders/queue/Waiting.vue'),
        meta: { title: '等待时间预估', icon: 'ClockCircleOutlined' }
      },
      {
        path: 'delivery-orders',
        name: 'OrdersDeliveryOrders',
        component: () => import('@/views/orders/delivery/Orders.vue'),
        meta: { title: '配送订单', icon: 'CarOutlined' }
      },
      {
        path: 'delivery-staff',
        name: 'OrdersDeliveryStaff',
        component: () => import('@/views/orders/delivery/Staff.vue'),
        meta: { title: '配送员管理', icon: 'UserOutlined' }
      },
      {
        path: 'delivery-routes',
        name: 'OrdersDeliveryRoutes',
        component: () => import('@/views/orders/delivery/Routes.vue'),
        meta: { title: '配送路线规划', icon: 'EnvironmentOutlined' }
      },
      {
        path: 'payment-methods',
        name: 'OrdersPaymentMethods',
        component: () => import('@/views/orders/payment/Methods.vue'),
        meta: { title: '支付方式设置', icon: 'CreditCardOutlined' }
      },
      {
        path: 'payment-records',
        name: 'OrdersPaymentRecords',
        component: () => import('@/views/orders/payment/Records.vue'),
        meta: { title: '交易记录', icon: 'HistoryOutlined' }
      },
      {
        path: 'payment-reconciliation',
        name: 'OrdersPaymentReconciliation',
        component: () => import('@/views/orders/payment/Reconciliation.vue'),
        meta: { title: '对账管理', icon: 'ReconciliationOutlined' }
      }
    ]
  },
  // 📦 库存管理
  {
    path: '/inventory',
    component: Layout,
    redirect: '/inventory/ingredients-archive',
    meta: { title: '库存管理', icon: 'InboxOutlined' },
    children: [
      {
        path: 'ingredients-archive',
        name: 'InventoryIngredientsArchive',
        component: () => import('@/views/inventory/ingredients/Archive.vue'),
        meta: { title: '食材档案', icon: 'AppleOutlined' }
      },
      {
        path: 'ingredients-category',
        name: 'InventoryIngredientsCategory',
        component: () => import('@/views/inventory/ingredients/Category.vue'),
        meta: { title: '食材分类', icon: 'FolderOutlined' }
      },
      {
        path: 'ingredients-expiry',
        name: 'InventoryIngredientsExpiry',
        component: () => import('@/views/inventory/ingredients/Expiry.vue'),
        meta: { title: '保质期管理', icon: 'ClockCircleOutlined' }
      },
      {
        path: 'inbound-register',
        name: 'InventoryInboundRegister',
        component: () => import('@/views/inventory/inbound/Register.vue'),
        meta: { title: '入库登记', icon: 'ImportOutlined' }
      },
      {
        path: 'inbound-audit',
        name: 'InventoryInboundAudit',
        component: () => import('@/views/inventory/inbound/Audit.vue'),
        meta: { title: '入库审核', icon: 'AuditOutlined' }
      },
      {
        path: 'inbound-quality',
        name: 'InventoryInboundQuality',
        component: () => import('@/views/inventory/inbound/Quality.vue'),
        meta: { title: '质检记录', icon: 'SafetyCertificateOutlined' }
      },
      {
        path: 'outbound-apply',
        name: 'InventoryOutboundApply',
        component: () => import('@/views/inventory/outbound/Apply.vue'),
        meta: { title: '出库申请', icon: 'ExportOutlined' }
      },
      {
        path: 'outbound-audit',
        name: 'InventoryOutboundAudit',
        component: () => import('@/views/inventory/outbound/Audit.vue'),
        meta: { title: '出库审核', icon: 'AuditOutlined' }
      },
      {
        path: 'outbound-usage',
        name: 'InventoryOutboundUsage',
        component: () => import('@/views/inventory/outbound/Usage.vue'),
        meta: { title: '用料统计', icon: 'BarChartOutlined' }
      },
      {
        path: 'monitor-query',
        name: 'InventoryMonitorQuery',
        component: () => import('@/views/inventory/monitor/Query.vue'),
        meta: { title: '库存查询', icon: 'SearchOutlined' }
      },
      {
        path: 'monitor-alert',
        name: 'InventoryMonitorAlert',
        component: () => import('@/views/inventory/monitor/Alert.vue'),
        meta: { title: '库存预警', icon: 'AlertOutlined' }
      },
      {
        path: 'monitor-stocktaking',
        name: 'InventoryMonitorStocktaking',
        component: () => import('@/views/inventory/monitor/Stocktaking.vue'),
        meta: { title: '盘点管理', icon: 'CheckSquareOutlined' }
      },
      {
        path: 'monitor-loss',
        name: 'InventoryMonitorLoss',
        component: () => import('@/views/inventory/monitor/Loss.vue'),
        meta: { title: '损耗统计', icon: 'FallOutlined' }
      }
    ]
  },
  {
    path: '/procurement',
    component: Layout,
    redirect: '/procurement/suppliers',
    meta: { title: '采购管理', icon: 'ShoppingOutlined' },
    children: [
      {
        path: 'suppliers',
        name: 'ProcurementSuppliers',
        component: () => import('@/views/procurement/Suppliers.vue'),
        meta: { title: '供应商管理', icon: 'BankOutlined' }
      },
      {
        path: 'planning',
        name: 'ProcurementPlanning',
        component: () => import('@/views/procurement/Planning.vue'),
        meta: { title: '采购计划', icon: 'ScheduleOutlined' }
      },
      {
        path: 'orders',
        name: 'ProcurementOrders',
        component: () => import('@/views/procurement/Orders.vue'),
        meta: { title: '采购订单', icon: 'FileTextOutlined' }
      },
      {
        path: 'analysis',
        name: 'ProcurementAnalysis',
        component: () => import('@/views/procurement/Analysis.vue'),
        meta: { title: '采购分析', icon: 'BarChartOutlined' }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/revenue',
    meta: { title: '财务管理', icon: 'AccountBookOutlined' },
    children: [
      {
        path: 'revenue',
        name: 'FinanceRevenue',
        component: () => import('@/views/finance/Revenue.vue'),
        meta: { title: '收入管理', icon: 'RiseOutlined' }
      },
      {
        path: 'cost',
        name: 'FinanceCost',
        component: () => import('@/views/finance/Cost.vue'),
        meta: { title: '成本管理', icon: 'FallOutlined' }
      },
      {
        path: 'profit',
        name: 'FinanceProfit',
        component: () => import('@/views/finance/Profit.vue'),
        meta: { title: '利润分析', icon: 'LineChartOutlined' }
      },
      {
        path: 'reports',
        name: 'FinanceReports',
        component: () => import('@/views/finance/Reports.vue'),
        meta: { title: '财务报表', icon: 'FileExcelOutlined' }
      }
    ]
  },
  {
    path: '/nutrition',
    component: Layout,
    redirect: '/nutrition/meal-planning',
    meta: { title: '营养健康', icon: 'HeartOutlined' },
    children: [
      {
        path: 'meal-planning',
        name: 'NutritionMealPlanning',
        component: () => import('@/views/nutrition/MealPlanning.vue'),
        meta: { title: '营养配餐', icon: 'AppleOutlined' }
      },
      {
        path: 'health',
        name: 'NutritionHealth',
        component: () => import('@/views/nutrition/Health.vue'),
        meta: { title: '健康管理', icon: 'UserOutlined' }
      },
      {
        path: 'analysis',
        name: 'NutritionAnalysis',
        component: () => import('@/views/nutrition/Analysis.vue'),
        meta: { title: '营养分析', icon: 'PieChartOutlined' }
      },
      {
        path: 'safety',
        name: 'NutritionSafety',
        component: () => import('@/views/nutrition/Safety.vue'),
        meta: { title: '食品安全', icon: 'SafetyCertificateOutlined' }
      }
    ]
  },
  {
    path: '/analytics',
    component: Layout,
    redirect: '/analytics/sales',
    meta: { title: '数据分析', icon: 'BarChartOutlined' },
    children: [
      {
        path: 'sales',
        name: 'AnalyticsSales',
        component: () => import('@/views/analytics/Sales.vue'),
        meta: { title: '销售分析', icon: 'LineChartOutlined' }
      },
      {
        path: 'users',
        name: 'AnalyticsUsers',
        component: () => import('@/views/analytics/Users.vue'),
        meta: { title: '用户分析', icon: 'UserOutlined' }
      },
      {
        path: 'operations',
        name: 'AnalyticsOperations',
        component: () => import('@/views/analytics/Operations.vue'),
        meta: { title: '运营分析', icon: 'SettingOutlined' }
      },
      {
        path: 'forecast',
        name: 'AnalyticsForecast',
        component: () => import('@/views/analytics/Forecast.vue'),
        meta: { title: '预测分析', icon: 'RocketOutlined' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: { title: '系统管理', icon: 'SettingOutlined' },
    children: [
      {
        path: 'users',
        name: 'SystemUsers',
        component: () => import('@/views/system/Users.vue'),
        meta: { title: '用户管理', icon: 'UserOutlined' }
      },
      {
        path: 'roles',
        name: 'SystemRoles',
        component: () => import('@/views/system/Roles.vue'),
        meta: { title: '角色权限', icon: 'TeamOutlined' }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/Config.vue'),
        meta: { title: '系统配置', icon: 'ToolOutlined' }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/system/Logs.vue'),
        meta: { title: '日志管理', icon: 'FileTextOutlined' }
      },
      {
        path: 'data',
        name: 'SystemData',
        component: () => import('@/views/system/Data.vue'),
        meta: { title: '数据管理', icon: 'DatabaseOutlined' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router