<template>
  <a-layout class="layout-container">
    <!-- 侧边栏 -->
    <a-layout-sider 
      v-model:collapsed="collapsed" 
      :trigger="null" 
      collapsible
      class="layout-sider"
      :width="256"
    >
      <!-- Logo区域 -->
      <div class="logo">
        <h2 v-if="!collapsed">智慧食堂管理平台</h2>
        <h2 v-else>智慧</h2>
      </div>
      
      <!-- 导航菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        theme="dark"
        mode="inline"
        @click="handleMenuClick"
      >
        <template v-for="route in menuRoutes" :key="route.path">
          <a-menu-item 
            v-if="!route.children || route.children.length <= 1"
            :key="route.path"
          >
            <component :is="route.meta.icon" />
            <span>{{ route.meta.title }}</span>
          </a-menu-item>
          
          <a-sub-menu v-else :key="route.path">
            <template #icon>
              <component :is="route.meta.icon" />
            </template>
            <template #title>{{ route.meta.title }}</template>
            <a-menu-item
              v-for="child in route.children.filter(c => !c.hidden)"
              :key="route.path + '/' + child.path"
            >
              <component :is="child.meta.icon" />
              <span>{{ child.meta.title }}</span>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </a-layout-sider>

    <a-layout>
      <!-- 头部 -->
      <a-layout-header class="layout-header">
        <div class="header-content">
          <!-- 折叠按钮 -->
          <a-button 
            type="text" 
            @click="toggleCollapsed"
            class="trigger"
          >
            <menu-unfold-outlined v-if="collapsed" />
            <menu-fold-outlined v-else />
          </a-button>

          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              <component :is="item.icon" v-if="item.icon" />
              {{ item.title }}
            </a-breadcrumb-item>
          </a-breadcrumb>

          <!-- 用户操作区 -->
          <div class="header-actions">
            <!-- 通知 -->
            <a-badge :count="5" :offset="[10, 0]">
              <a-button type="text" size="large">
                <bell-outlined />
              </a-button>
            </a-badge>

            <!-- 用户菜单 -->
            <a-dropdown>
              <a-button type="text" class="user-dropdown">
                <a-avatar size="small" style="margin-right: 8px">
                  <template #icon>
                    <user-outlined />
                  </template>
                </a-avatar>
                系统管理员
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="profile">
                    <user-outlined />
                    个人资料
                  </a-menu-item>
                  <a-menu-item key="settings">
                    <setting-outlined />
                    系统设置
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout">
                    <logout-outlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="layout-content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  BellOutlined,
  UserOutlined,
  DownOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'Layout',
  components: {
    MenuUnfoldOutlined,
    MenuFoldOutlined,
    BellOutlined,
    UserOutlined,
    DownOutlined,
    SettingOutlined,
    LogoutOutlined
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const collapsed = ref(false)
    const selectedKeys = ref([])
    const openKeys = ref([])

    // 获取菜单路由
    const menuRoutes = computed(() => {
      return router.getRoutes().filter(route =>
        route.path !== '/' &&
        route.meta?.title &&
        route.component?.name === 'Layout'
      )
    })

    // 面包屑导航
    const breadcrumbItems = computed(() => {
      const matched = route.matched.filter(item => item.meta && item.meta.title)
      const breadcrumbs = []
      
      matched.forEach(item => {
        if (item.meta.title) {
          breadcrumbs.push({
            path: item.path,
            title: item.meta.title,
            icon: item.meta.icon
          })
        }
      })
      
      return breadcrumbs
    })

    // 切换侧边栏
    const toggleCollapsed = () => {
      collapsed.value = !collapsed.value
    }

    // 处理菜单点击
    const handleMenuClick = ({ key }) => {
      if (key !== route.path) {
        router.push(key)
      }
    }

    // 监听路由变化，更新选中状态
    watch(
      () => route.path,
      (newPath) => {
        selectedKeys.value = [newPath]
        
        // 设置展开的菜单
        const pathSegments = newPath.split('/').filter(Boolean)
        if (pathSegments.length > 1) {
          openKeys.value = ['/' + pathSegments[0]]
        }
      },
      { immediate: true }
    )

    return {
      collapsed,
      selectedKeys,
      openKeys,
      menuRoutes,
      breadcrumbItems,
      toggleCollapsed,
      handleMenuClick
    }
  }
}
</script>

<style scoped>
.logo {
  height: 64px;
  padding: 16px;
  color: #fff;
  text-align: center;
  border-bottom: 1px solid #ffffff0f;
}

.logo h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.breadcrumb {
  margin-left: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 12px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-dropdown:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    position: fixed !important;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
  }
  
  .header-content {
    padding: 0 16px;
  }
  
  .breadcrumb {
    display: none;
  }
}
</style>