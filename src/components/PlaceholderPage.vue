<template>
  <div class="placeholder-page">
    <div class="page-title">{{ title }}</div>
    
    <a-card class="placeholder-card">
      <div class="placeholder-content">
        <a-empty :description="description">
          <template #image>
            <component :is="icon" style="font-size: 64px; color: #d9d9d9;" />
          </template>
          <a-button type="primary" @click="handleAction">
            {{ actionText }}
          </a-button>
        </a-empty>
      </div>
    </a-card>
    
    <!-- 功能说明 -->
    <a-card title="功能说明" style="margin-top: 24px;" v-if="features.length > 0">
      <a-list :data-source="features" item-layout="horizontal">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :style="{ backgroundColor: '#1890ff' }">
                  <template #icon>
                    <check-outlined />
                  </template>
                </a-avatar>
              </template>
              <template #title>{{ item.title }}</template>
              <template #description>{{ item.description }}</template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script>
import { message } from 'ant-design-vue'
import { CheckOutlined } from '@ant-design/icons-vue'

export default {
  name: 'PlaceholderPage',
  components: {
    CheckOutlined
  },
  props: {
    title: {
      type: String,
      default: '页面开发中'
    },
    description: {
      type: String,
      default: '该功能正在开发中，敬请期待'
    },
    icon: {
      type: String,
      default: 'ToolOutlined'
    },
    actionText: {
      type: String,
      default: '了解更多'
    },
    features: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const handleAction = () => {
      message.info('功能开发中，敬请期待！')
    }

    return {
      handleAction
    }
  }
}
</script>

<style scoped>
.placeholder-page {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

.placeholder-card {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  width: 100%;
}
</style>
