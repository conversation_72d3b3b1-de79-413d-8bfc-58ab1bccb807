declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    accessibility: {
        type: BooleanConstructor;
        default: boolean;
    };
    adaptiveHeight: {
        type: BooleanConstructor;
        default: boolean;
    };
    afterChange: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    arrows: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoplay: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoplaySpeed: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    beforeChange: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    centerMode: {
        type: BooleanConstructor;
        default: boolean;
    };
    centerPadding: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    cssEase: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    dots: {
        type: BooleanConstructor;
        default: boolean;
    };
    dotsClass: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    unslick: {
        type: BooleanConstructor;
        default: boolean;
    };
    easing: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    edgeFriction: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    fade: {
        type: BooleanConstructor;
        default: boolean;
    };
    focusOnSelect: {
        type: BooleanConstructor;
        default: boolean;
    };
    infinite: {
        type: BooleanConstructor;
        default: boolean;
    };
    initialSlide: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    lazyLoad: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    verticalSwiping: {
        type: BooleanConstructor;
        default: boolean;
    };
    asNavFor: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    pauseOnDotsHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    pauseOnFocus: {
        type: BooleanConstructor;
        default: boolean;
    };
    pauseOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    responsive: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    };
    rows: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    rtl: {
        type: BooleanConstructor;
        default: boolean;
    };
    slide: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    slidesPerRow: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    slidesToScroll: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    slidesToShow: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    speed: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    swipe: {
        type: BooleanConstructor;
        default: boolean;
    };
    swipeEvent: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    swipeToSlide: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchMove: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchThreshold: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    useCSS: {
        type: BooleanConstructor;
        default: boolean;
    };
    useTransform: {
        type: BooleanConstructor;
        default: boolean;
    };
    variableWidth: {
        type: BooleanConstructor;
        default: boolean;
    };
    vertical: {
        type: BooleanConstructor;
        default: boolean;
    };
    waitForAnimate: {
        type: BooleanConstructor;
        default: boolean;
    };
    children: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    };
    __propsSymbol__: import("vue-types").VueTypeValidableDef<any>;
}>, {}, {
    breakpoint: any;
}, {}, {
    innerSliderRefHandler(ref: any): void;
    media(query: any, handler: any): void;
    slickPrev(): void;
    slickNext(): void;
    slickGoTo(slide: any, dontAnimate?: boolean): void;
    slickPause(): void;
    slickPlay(): void;
}, {
    methods: {
        setState(state: {}, callback: () => any): void;
        __emit(): void;
    };
}, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    accessibility: {
        type: BooleanConstructor;
        default: boolean;
    };
    adaptiveHeight: {
        type: BooleanConstructor;
        default: boolean;
    };
    afterChange: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    arrows: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoplay: {
        type: BooleanConstructor;
        default: boolean;
    };
    autoplaySpeed: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    beforeChange: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    centerMode: {
        type: BooleanConstructor;
        default: boolean;
    };
    centerPadding: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    cssEase: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    dots: {
        type: BooleanConstructor;
        default: boolean;
    };
    dotsClass: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    unslick: {
        type: BooleanConstructor;
        default: boolean;
    };
    easing: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    edgeFriction: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    fade: {
        type: BooleanConstructor;
        default: boolean;
    };
    focusOnSelect: {
        type: BooleanConstructor;
        default: boolean;
    };
    infinite: {
        type: BooleanConstructor;
        default: boolean;
    };
    initialSlide: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    lazyLoad: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    verticalSwiping: {
        type: BooleanConstructor;
        default: boolean;
    };
    asNavFor: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    pauseOnDotsHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    pauseOnFocus: {
        type: BooleanConstructor;
        default: boolean;
    };
    pauseOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    responsive: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    };
    rows: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    rtl: {
        type: BooleanConstructor;
        default: boolean;
    };
    slide: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    slidesPerRow: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    slidesToScroll: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    slidesToShow: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    speed: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    swipe: {
        type: BooleanConstructor;
        default: boolean;
    };
    swipeEvent: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    swipeToSlide: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchMove: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchThreshold: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    useCSS: {
        type: BooleanConstructor;
        default: boolean;
    };
    useTransform: {
        type: BooleanConstructor;
        default: boolean;
    };
    variableWidth: {
        type: BooleanConstructor;
        default: boolean;
    };
    vertical: {
        type: BooleanConstructor;
        default: boolean;
    };
    waitForAnimate: {
        type: BooleanConstructor;
        default: boolean;
    };
    children: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    };
    __propsSymbol__: import("vue-types").VueTypeValidableDef<any>;
}>> & Readonly<{}>, {
    slidesToShow: number;
    centerMode: boolean;
    rtl: boolean;
    centerPadding: string;
    vertical: boolean;
    waitForAnimate: boolean;
    fade: boolean;
    infinite: boolean;
    lazyLoad: any;
    slidesToScroll: number;
    useCSS: boolean;
    dots: boolean;
    children: unknown[];
    swipeToSlide: boolean;
    verticalSwiping: boolean;
    edgeFriction: number;
    swipeEvent: any;
    swipe: boolean;
    touchThreshold: number;
    variableWidth: boolean;
    speed: number;
    cssEase: string;
    dotsClass: string;
    initialSlide: number;
    adaptiveHeight: boolean;
    asNavFor: any;
    beforeChange: any;
    afterChange: any;
    accessibility: boolean;
    draggable: boolean;
    autoplaySpeed: number;
    autoplay: boolean;
    focusOnSelect: boolean;
    unslick: boolean;
    pauseOnHover: boolean;
    pauseOnDotsHover: boolean;
    arrows: boolean;
    touchMove: boolean;
    easing: string;
    pauseOnFocus: boolean;
    responsive: unknown[];
    rows: number;
    slide: string;
    slidesPerRow: number;
    useTransform: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
